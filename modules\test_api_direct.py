#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试API接口
"""

import requests
import json

def test_api():
    """测试API接口"""
    print("🧪 直接测试API接口...")
    
    # 测试数据
    test_data = {
        "keyword": "蓝牙耳机",
        "max_products": 2,
        "max_reviews": 10,
        "include_reviews": True,
        "enable_debug": True
    }
    
    print(f"📤 发送请求: {json.dumps(test_data, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/crawl",
            json=test_data,
            timeout=120
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📥 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ 响应数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            except:
                print(f"📄 响应文本: {response.text}")
        else:
            print(f"❌ 错误响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_api()
