#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Dify工作流文件
对比原始文件和修改后文件的差异
"""

import yaml
import json

def load_yaml_file(filepath):
    """加载YAML文件"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    except Exception as e:
        print(f"❌ 加载文件失败 {filepath}: {e}")
        return None

def compare_structure(original, modified, path=""):
    """对比两个文件的结构"""
    differences = []
    
    if type(original) != type(modified):
        differences.append(f"{path}: 类型不同 {type(original)} vs {type(modified)}")
        return differences
    
    if isinstance(original, dict):
        # 检查键
        orig_keys = set(original.keys())
        mod_keys = set(modified.keys())
        
        missing_keys = orig_keys - mod_keys
        extra_keys = mod_keys - orig_keys
        
        if missing_keys:
            differences.append(f"{path}: 缺少键 {missing_keys}")
        if extra_keys:
            differences.append(f"{path}: 额外键 {extra_keys}")
        
        # 递归检查共同键
        for key in orig_keys & mod_keys:
            new_path = f"{path}.{key}" if path else key
            differences.extend(compare_structure(original[key], modified[key], new_path))
    
    elif isinstance(original, list):
        if len(original) != len(modified):
            differences.append(f"{path}: 列表长度不同 {len(original)} vs {len(modified)}")
        else:
            for i, (orig_item, mod_item) in enumerate(zip(original, modified)):
                new_path = f"{path}[{i}]"
                differences.extend(compare_structure(orig_item, mod_item, new_path))
    
    return differences

def analyze_nodes(data):
    """分析节点信息"""
    if not data or 'workflow' not in data or 'graph' not in data['workflow']:
        return {}
    
    nodes = data['workflow']['graph'].get('nodes', [])
    edges = data['workflow']['graph'].get('edges', [])
    
    node_info = {}
    for node in nodes:
        node_data = node.get('data', {})
        node_info[node['id']] = {
            'title': node_data.get('title', ''),
            'type': node_data.get('type', ''),
            'tool_name': node_data.get('tool_name', ''),
            'position': node.get('position', {})
        }
    
    return {
        'nodes': node_info,
        'node_count': len(nodes),
        'edge_count': len(edges),
        'env_vars': data['workflow'].get('environment_variables', [])
    }

def main():
    """主函数"""
    print("🔍 Dify工作流文件验证工具")
    print("=" * 60)
    
    # 加载文件
    original_file = "步骤4.7-用户体验优化版.yml"
    modified_file = "步骤4.14-完全兼容版.yml"
    
    print(f"📂 加载原始文件: {original_file}")
    original_data = load_yaml_file(original_file)
    
    print(f"📂 加载修改文件: {modified_file}")
    modified_data = load_yaml_file(modified_file)
    
    if not original_data or not modified_data:
        print("❌ 文件加载失败")
        return
    
    print("✅ 文件加载成功")
    
    # 分析节点信息
    print("\n📊 节点分析:")
    orig_analysis = analyze_nodes(original_data)
    mod_analysis = analyze_nodes(modified_data)
    
    print(f"原始文件: {orig_analysis['node_count']} 个节点, {orig_analysis['edge_count']} 个连接")
    print(f"修改文件: {mod_analysis['node_count']} 个节点, {mod_analysis['edge_count']} 个连接")
    
    # 检查节点差异
    print("\n🔍 节点对比:")
    for node_id, info in orig_analysis['nodes'].items():
        if node_id in mod_analysis['nodes']:
            mod_info = mod_analysis['nodes'][node_id]
            if info['title'] != mod_info['title']:
                print(f"  📝 {node_id}: 标题变更")
                print(f"     原始: {info['title']}")
                print(f"     修改: {mod_info['title']}")
        else:
            print(f"  ❌ {node_id}: 节点缺失")
    
    # 检查环境变量
    print("\n🔧 环境变量:")
    print(f"原始文件: {len(orig_analysis['env_vars'])} 个")
    print(f"修改文件: {len(mod_analysis['env_vars'])} 个")
    
    for env_var in mod_analysis['env_vars']:
        print(f"  ✅ {env_var['key']}: {env_var['value']}")
    
    # 检查关键差异
    print("\n🎯 关键修改:")
    
    # 检查淘宝节点
    taobao_node_orig = orig_analysis['nodes'].get('taobao_search', {})
    taobao_node_mod = mod_analysis['nodes'].get('taobao_search', {})
    
    if taobao_node_orig and taobao_node_mod:
        print(f"  🛒 淘宝节点:")
        print(f"     原始: {taobao_node_orig['title']}")
        print(f"     修改: {taobao_node_mod['title']}")
    
    # 验证文件完整性
    print("\n✅ 验证结果:")
    
    if mod_analysis['node_count'] == orig_analysis['node_count']:
        print("  ✅ 节点数量一致")
    else:
        print("  ❌ 节点数量不一致")
    
    if mod_analysis['edge_count'] == orig_analysis['edge_count']:
        print("  ✅ 连接数量一致")
    else:
        print("  ❌ 连接数量不一致")
    
    if len(mod_analysis['env_vars']) > 0:
        print("  ✅ 包含环境变量")
    else:
        print("  ❌ 缺少环境变量")
    
    print(f"\n🎉 验证完成！修改后的文件应该可以正常导入Dify")
    print(f"📁 推荐使用文件: {modified_file}")

if __name__ == "__main__":
    main()
