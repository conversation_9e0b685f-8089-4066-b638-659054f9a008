#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接运行MCP服务器进行测试
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from taobao_mcp_server import TaobaoMCPServer

async def run_server_direct():
    """直接运行MCP服务器"""
    print("🚀 启动淘宝爬虫MCP服务器")
    print("=" * 50)
    print("使用标准输入/输出进行MCP通信")
    print("服务器将等待MCP客户端连接...")
    print()
    
    try:
        # 创建并运行服务器
        server = TaobaoMCPServer()
        await server.run()
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器运行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(run_server_direct())
