#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MCP数据格式和传输正确性
验证数据是否能正确传递给智能分析引擎
"""

import json
import requests
import time

def test_mcp_data_format():
    """测试MCP数据格式"""
    print("🔍 测试MCP数据格式和传输")
    print("=" * 50)
    
    # 模拟MCP工具返回的数据格式
    sample_mcp_data = {
        "success": True,
        "platform": "taobao",
        "keyword": "蓝牙耳机",
        "total_products": 3,
        "total_reviews": 30,
        "data_quality": {
            "real_review_rate": 0.944,
            "negative_review_rate": 0.333,
            "data_completeness": 1.0
        },
        "products": [
            {
                "title": "小米蓝牙耳机Air2 SE",
                "price": "99.00",
                "sales": "10万+",
                "rating": 4.8,
                "shop_name": "小米官方旗舰店",
                "product_url": "https://detail.tmall.com/item.htm?id=123456",
                "image_url": "https://img.alicdn.com/imgextra/i1/123456.jpg",
                "reviews": [
                    {
                        "username": "用户1",
                        "rating": 5,
                        "content": "音质很好，连接稳定，性价比高",
                        "date": "2024-01-15",
                        "tags": ["音质好", "性价比高", "连接稳定"],
                        "sentiment": "positive",
                        "helpful_count": 15
                    },
                    {
                        "username": "用户2", 
                        "rating": 2,
                        "content": "电池续航不行，经常断连",
                        "date": "2024-01-10",
                        "tags": ["续航差", "连接问题"],
                        "sentiment": "negative",
                        "helpful_count": 8
                    }
                ]
            }
        ],
        "analysis": {
            "good_review_themes": ["音质好", "性价比高", "外观漂亮"],
            "bad_review_issues": ["续航差", "连接问题", "做工粗糙"],
            "user_needs": ["高音质", "长续航", "稳定连接", "舒适佩戴"],
            "market_insights": {
                "price_range": "50-300元",
                "main_competitors": ["苹果AirPods", "华为FreeBuds", "OPPO Enco"],
                "growth_trend": "上升",
                "seasonal_pattern": "双11、618销量激增"
            }
        },
        "metadata": {
            "crawl_time": "2024-01-20 10:30:00",
            "mcp_version": "1.12.2",
            "data_source": "MCP Protocol",
            "processing_time": 15.6
        }
    }
    
    print("📊 MCP数据结构验证:")
    print(f"   ✅ 数据完整性: {len(sample_mcp_data)} 个主要字段")
    print(f"   ✅ 商品数量: {sample_mcp_data['total_products']}")
    print(f"   ✅ 评价数量: {sample_mcp_data['total_reviews']}")
    print(f"   ✅ 真实评价率: {sample_mcp_data['data_quality']['real_review_rate']*100}%")
    print(f"   ✅ 差评获取率: {sample_mcp_data['data_quality']['negative_review_rate']*100}%")
    
    # 验证数据结构
    required_fields = ['success', 'platform', 'keyword', 'products', 'analysis']
    missing_fields = [field for field in required_fields if field not in sample_mcp_data]
    
    if missing_fields:
        print(f"   ❌ 缺少必需字段: {missing_fields}")
        return False
    else:
        print("   ✅ 所有必需字段都存在")
    
    # 验证产品数据结构
    if sample_mcp_data['products']:
        product = sample_mcp_data['products'][0]
        product_fields = ['title', 'price', 'rating', 'reviews']
        missing_product_fields = [field for field in product_fields if field not in product]
        
        if missing_product_fields:
            print(f"   ❌ 产品数据缺少字段: {missing_product_fields}")
            return False
        else:
            print("   ✅ 产品数据结构完整")
    
    # 验证评价数据结构
    if sample_mcp_data['products'][0]['reviews']:
        review = sample_mcp_data['products'][0]['reviews'][0]
        review_fields = ['username', 'rating', 'content', 'sentiment', 'tags']
        missing_review_fields = [field for field in review_fields if field not in review]
        
        if missing_review_fields:
            print(f"   ❌ 评价数据缺少字段: {missing_review_fields}")
            return False
        else:
            print("   ✅ 评价数据结构完整")
    
    # 验证分析数据
    analysis_fields = ['good_review_themes', 'bad_review_issues', 'user_needs']
    missing_analysis_fields = [field for field in analysis_fields if field not in sample_mcp_data['analysis']]
    
    if missing_analysis_fields:
        print(f"   ❌ 分析数据缺少字段: {missing_analysis_fields}")
        return False
    else:
        print("   ✅ 分析数据结构完整")
    
    return True

def test_json_serialization():
    """测试JSON序列化"""
    print("\n🔄 测试JSON序列化")
    print("-" * 30)
    
    test_data = {
        "keyword": "蓝牙耳机",
        "products": [
            {
                "title": "测试产品",
                "reviews": [
                    {"content": "很好用", "rating": 5},
                    {"content": "一般般", "rating": 3}
                ]
            }
        ]
    }
    
    try:
        json_str = json.dumps(test_data, ensure_ascii=False, indent=2)
        print("   ✅ JSON序列化成功")
        
        parsed_data = json.loads(json_str)
        print("   ✅ JSON反序列化成功")
        
        if parsed_data == test_data:
            print("   ✅ 数据完整性验证通过")
            return True
        else:
            print("   ❌ 数据完整性验证失败")
            return False
            
    except Exception as e:
        print(f"   ❌ JSON处理失败: {e}")
        return False

def test_dify_data_format():
    """测试Dify期望的数据格式"""
    print("\n📋 测试Dify数据格式兼容性")
    print("-" * 40)
    
    # Dify工作流期望的数据格式
    dify_expected_format = {
        "text": "MCP工具返回的文本数据",
        "json": {
            "success": True,
            "data": "结构化数据"
        }
    }
    
    # 模拟MCP工具的实际返回格式
    mcp_actual_format = {
        "success": True,
        "platform": "taobao", 
        "keyword": "蓝牙耳机",
        "products": [],
        "analysis": {}
    }
    
    print("   📊 Dify期望格式:")
    print(f"      - text字段: {'✅' if 'text' in dify_expected_format else '❌'}")
    print(f"      - json字段: {'✅' if 'json' in dify_expected_format else '❌'}")
    
    print("   📊 MCP实际格式:")
    print(f"      - success字段: {'✅' if 'success' in mcp_actual_format else '❌'}")
    print(f"      - platform字段: {'✅' if 'platform' in mcp_actual_format else '❌'}")
    print(f"      - keyword字段: {'✅' if 'keyword' in mcp_actual_format else '❌'}")
    
    # 建议的数据转换
    print("\n   💡 建议的数据格式:")
    suggested_format = {
        "text": json.dumps(mcp_actual_format, ensure_ascii=False, indent=2),
        "success": mcp_actual_format["success"],
        "platform": mcp_actual_format["platform"],
        "keyword": mcp_actual_format["keyword"],
        "data": mcp_actual_format
    }
    
    print("      ✅ 同时提供text和结构化字段")
    print("      ✅ 保持MCP数据完整性")
    print("      ✅ 兼容Dify工作流引用")
    
    return True

def generate_test_report():
    """生成测试报告"""
    print("\n" + "="*50)
    print("📋 MCP数据传输测试报告")
    print("="*50)
    
    tests = [
        ("MCP数据格式验证", test_mcp_data_format),
        ("JSON序列化测试", test_json_serialization), 
        ("Dify格式兼容性", test_dify_data_format)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    print("\n📊 测试结果汇总:")
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print(f"\n🎯 总体结果: {'✅ 全部通过' if all_passed else '❌ 存在问题'}")
    
    if all_passed:
        print("\n💡 建议:")
        print("   1. MCP工具数据格式符合要求")
        print("   2. 可以正常传递给智能分析引擎")
        print("   3. 建议在Dify中使用 {{#mcp_tool.text#}} 引用数据")
    else:
        print("\n🔧 修复建议:")
        print("   1. 检查MCP工具返回的数据结构")
        print("   2. 确保包含所有必需字段")
        print("   3. 验证JSON序列化正确性")

if __name__ == "__main__":
    generate_test_report()
