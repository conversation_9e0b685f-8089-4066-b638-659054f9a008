app:
  description: 电商爆品选品工作流 - 步骤2：多平台搜索工具
  icon: 🛍️
  icon_background: '#FF6B35'
  mode: advanced-chat
  name: 电商爆品选品工作流-步骤2
  use_icon_as_answer_icon: false

dependencies: []
kind: app
version: 0.3.0

workflow:
  conversation_variables: []
  environment_variables: []
  
  features:
    file_upload:
      enabled: false
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false

  graph:
    edges:
    # Start 到四个搜索工具的连接
    - data:
        isInLoop: false
        sourceType: start
        targetType: tool
      id: start-source-douyin_search-target
      source: start
      sourceHandle: source
      target: douyin_search
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: start
        targetType: tool
      id: start-source-taobao_search-target
      source: start
      sourceHandle: source
      target: taobao_search
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: start
        targetType: tool
      id: start-source-amazon_search-target
      source: start
      sourceHandle: source
      target: amazon_search
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: start
        targetType: tool
      id: start-source-pdd_search-target
      source: start
      sourceHandle: source
      target: pdd_search
      targetHandle: target
      type: custom
    
    # 四个搜索工具到LLM分析的连接
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: douyin_search-source-analysis-target
      source: douyin_search
      sourceHandle: source
      target: analysis
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: taobao_search-source-analysis-target
      source: taobao_search
      sourceHandle: source
      target: analysis
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: amazon_search-source-analysis-target
      source: amazon_search
      sourceHandle: source
      target: analysis
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: pdd_search-source-analysis-target
      source: pdd_search
      sourceHandle: source
      target: analysis
      targetHandle: target
      type: custom
    
    # LLM到Answer的连接
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: analysis-source-final_answer-target
      source: analysis
      sourceHandle: source
      target: final_answer
      targetHandle: target
      type: custom

    nodes:
    # 开始节点
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 商品类目
          max_length: 256
          options: []
          required: true
          type: text-input
          variable: category
      height: 116
      id: start
      position:
        x: -500
        y: 300
      positionAbsolute:
        x: -500
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 抖音搜索工具
    - data:
        desc: ''
        provider_id: builtin
        provider_name: builtin
        provider_type: builtin
        selected: false
        title: 抖音搜索
        tool_configurations: {}
        tool_label: HTTP Request
        tool_name: http_request
        tool_parameters:
          url:
            type: constant
            value: "https://httpbin.org/json"
          method:
            type: constant
            value: "GET"
          headers:
            type: constant
            value: "Content-Type: application/json\nPlatform: Douyin"
        type: tool
      height: 148
      id: douyin_search
      position:
        x: -200
        y: 150
      positionAbsolute:
        x: -200
        y: 150
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 淘宝搜索工具
    - data:
        desc: ''
        provider_id: builtin
        provider_name: builtin
        provider_type: builtin
        selected: false
        title: 淘宝搜索
        tool_configurations: {}
        tool_label: HTTP Request
        tool_name: http_request
        tool_parameters:
          url:
            type: constant
            value: "https://httpbin.org/json"
          method:
            type: constant
            value: "GET"
          headers:
            type: constant
            value: "Content-Type: application/json\nPlatform: Taobao"
        type: tool
      height: 148
      id: taobao_search
      position:
        x: -200
        y: 320
      positionAbsolute:
        x: -200
        y: 320
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 亚马逊搜索工具
    - data:
        desc: ''
        provider_id: builtin
        provider_name: builtin
        provider_type: builtin
        selected: false
        title: 亚马逊搜索
        tool_configurations: {}
        tool_label: HTTP Request
        tool_name: http_request
        tool_parameters:
          url:
            type: constant
            value: "https://httpbin.org/json"
          method:
            type: constant
            value: "GET"
          headers:
            type: constant
            value: "Content-Type: application/json\nPlatform: Amazon"
        type: tool
      height: 148
      id: amazon_search
      position:
        x: -200
        y: 490
      positionAbsolute:
        x: -200
        y: 490
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 拼多多搜索工具
    - data:
        desc: ''
        provider_id: builtin
        provider_name: builtin
        provider_type: builtin
        selected: false
        title: 拼多多搜索
        tool_configurations: {}
        tool_label: HTTP Request
        tool_name: http_request
        tool_parameters:
          url:
            type: constant
            value: "https://httpbin.org/json"
          method:
            type: constant
            value: "GET"
          headers:
            type: constant
            value: "Content-Type: application/json\nPlatform: Pinduoduo"
        type: tool
      height: 148
      id: pdd_search
      position:
        x: -200
        y: 660
      positionAbsolute:
        x: -200
        y: 660
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # LLM分析节点
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.3
            max_tokens: 2048
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        prompt_template:
        - role: system
          text: |
            你是一位资深的电商选品专家。请基于用户提供的商品类目和多平台搜索结果，给出全面的市场分析和选品建议。
            
            请用以下格式回答：
            
            ## 📊 {{category}} 多平台选品分析
            
            ### 🏪 平台数据概览
            - **抖音**: [基于抖音数据的分析]
            - **淘宝**: [基于淘宝数据的分析] 
            - **亚马逊**: [基于亚马逊数据的分析]
            - **拼多多**: [基于拼多多数据的分析]
            
            ### 📈 综合市场趋势
            [跨平台趋势分析]
            
            ### 🎯 选品建议
            1. **高潜力产品**：[推荐产品及理由]
            2. **市场空白**：[发现的机会点]
            3. **竞争分析**：[各平台竞争态势]
            
            ### ⚠️ 风险提示
            [需要注意的市场风险]
        - role: user
          text: |
            请基于以下多平台搜索结果进行综合分析：
            
            **商品类目**: {{#start.category#}}
            
            **抖音数据**: {{#douyin_search.body#}}
            **淘宝数据**: {{#taobao_search.body#}}
            **亚马逊数据**: {{#amazon_search.body#}}
            **拼多多数据**: {{#pdd_search.body#}}
        selected: false
        title: 多平台分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 96
      id: analysis
      position:
        x: 150
        y: 400
      positionAbsolute:
        x: 150
        y: 400
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 最终答案节点
    - data:
        answer: |
          {{#analysis.text#}}
          
          ---
          
          ### 📊 数据处理统计
          - **商品类目**: {{#start.category#}}
          - **抖音搜索**: ✅ 已完成
          - **淘宝搜索**: ✅ 已完成  
          - **亚马逊搜索**: ✅ 已完成
          - **拼多多搜索**: ✅ 已完成
          - **数据源**: 4个平台并行搜索
          
          💡 **下一步**: 我们将添加结构化数据输出和评分系统！
        desc: ''
        selected: false
        title: 多平台分析结果
        type: answer
        variables: []
      height: 185
      id: final_answer
      position:
        x: 450
        y: 400
      positionAbsolute:
        x: 450
        y: 400
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    viewport:
      x: 500
      y: -200
      zoom: 0.8 