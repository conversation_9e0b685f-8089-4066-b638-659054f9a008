# Dify HTTP MCP集成指南

## 🎯 概述

根据Dify官方MCP文档，我们已经成功创建了符合Dify要求的HTTP传输MCP服务器。

## 🚀 HTTP MCP服务器信息

### 服务器详情
- **服务器名称**: 淘宝爬虫MCP服务器
- **服务器URL**: `http://192.168.0.101:8001/mcp/`
- **传输协议**: HTTP (Streamable-HTTP)
- **协议版本**: MCP 1.12.2
- **FastMCP版本**: 2.10.6
- **主机绑定**: 192.168.0.101 (局域网地址，避免代理问题)
- **局域网访问**: 支持

### 可用工具
1. **crawl_taobao_products**
   - 描述: 爬取淘宝商品数据，包括商品信息和用户评价
   - 参数: keyword, max_products, max_reviews, include_reviews, enable_debug

2. **get_crawler_status**
   - 描述: 获取爬虫状态和版本信息
   - 参数: 无

3. **health_check**
   - 描述: 健康检查端点
   - 参数: 无

## 📋 在Dify中添加MCP服务器

### 步骤1: 访问MCP管理
1. 在Dify工作区中导航到 **Tools** → **MCP**
2. 点击 **Add MCP Server (HTTP)**

### 步骤2: 配置服务器
- **Server URL**: `http://192.168.0.101:8001/mcp/`
- **Name**: `淘宝爬虫MCP服务器`
- **Icon**: 可选择自定义图标
- **Server Identifier**: `taobao-crawler-http` (重要：不要更改此ID)

### 步骤3: 授权和工具发现
Dify将自动：
1. 发现可用工具
2. 处理授权（如果需要）
3. 获取工具定义
4. 更新工具清单

## 🛠️ 在应用中使用MCP工具

### Agent应用
1. 在Agent配置中选择工具
2. MCP工具显示为: "淘宝爬虫MCP服务器 » crawl_taobao_products"
3. 可以使用"Add All"快速启用所有工具

### 工作流应用
1. MCP工具作为可用节点类型出现
2. 每个工具节点显示来源服务器
3. 复杂参数使用JSON输入界面

### Agent节点（在工作流中）
1. 在Agent节点中选择MCP工具
2. 与独立Agent相同的使用方式

## ⚙️ 工具参数配置

### 推荐配置
对于 `crawl_taobao_products` 工具：

- **keyword**: Auto (让AI根据上下文决定)
- **max_products**: Fixed Value = 3 (固定值，控制响应大小)
- **max_reviews**: Fixed Value = 10 (固定值，平衡质量和速度)
- **include_reviews**: Fixed Value = true (固定值，确保包含评价)
- **enable_debug**: Fixed Value = false (固定值，生产环境关闭调试)

### 配置原理
- **Auto参数**: 让AI模型根据用户输入自动确定值
- **Fixed Value**: 设置一致的配置值，简化AI推理过程

## 🔧 启动服务器

### 方法1: 直接启动
```bash
# 确保在torch conda环境中
conda activate torch

# 启动HTTP MCP服务器
cd modules
python taobao_mcp_http_server.py
```

### 方法2: 使用完整路径
```bash
# 使用完整Python路径
/c/Users/<USER>/.conda/envs/torch/python.exe taobao_mcp_http_server.py
```

### 验证服务器运行
```bash
# 检查MCP端点
curl http://localhost:8000/mcp/

# 应该返回MCP协议响应（不是404）
```

## 📊 应用可移植性

### DSL导出
- 导出的DSL包含MCP服务器ID引用
- 服务器ID: `taobao-crawler-http`

### 环境迁移
1. 在目标环境中添加相同的MCP服务器
2. 使用相同的服务器ID: `taobao-crawler-http`
3. 确保服务器URL可访问

### 共享应用
文档化依赖：
- MCP服务器URL: `http://localhost:8000/mcp/`
- 必需服务器ID: `taobao-crawler-http`
- 依赖工具: crawl_taobao_products, get_crawler_status

## 🚨 故障排除

### 常见问题
1. **"Unconfigured Server"**
   - 检查服务器URL是否可访问
   - 重新授权服务器

2. **"Missing Tools"**
   - 点击"Update Tools"刷新
   - 检查服务器是否正常运行

3. **"Broken Applications"**
   - 确保服务器ID未更改
   - 重新添加服务器（使用原始ID）

### 调试步骤
```bash
# 1. 检查服务器状态
curl http://localhost:8000/mcp/

# 2. 检查Python环境
conda activate torch
python -c "from fastmcp import FastMCP; print('FastMCP OK')"

# 3. 重启服务器
python taobao_mcp_http_server.py
```

## ✅ 最佳实践

### 服务器管理
1. **一致的服务器ID**: 使用描述性、永久的ID
2. **环境一致性**: 在开发、测试、生产环境保持相同配置
3. **工具自定义**: 利用参数配置优化工具行为

### 应用开发
1. **工具文档**: 记录应用使用的外部工具
2. **渐进更新**: 在开发环境测试工具更改
3. **备份计划**: 考虑外部MCP服务器不可用的情况

## 🎉 总结

现在您已经拥有了：
- ✅ 符合Dify要求的HTTP MCP服务器
- ✅ 完整的集成指南
- ✅ 故障排除方案
- ✅ 最佳实践建议

您可以在Dify中添加这个MCP服务器，并在Agent和工作流应用中使用淘宝爬虫功能！
