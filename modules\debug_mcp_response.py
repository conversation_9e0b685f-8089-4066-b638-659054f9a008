#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试MCP响应格式
"""

import asyncio
import json
import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def debug_mcp_response():
    """调试MCP响应格式"""
    print("🔍 调试MCP响应格式")
    print("=" * 40)
    
    try:
        server_path = r"c:\Users\<USER>\Desktop\dify_test\modules\taobao_mcp_server.py"
        server_params = StdioServerParameters(
            command="python",
            args=[server_path],
            env=os.environ.copy()
        )
        
        async with stdio_client(server_params) as (read_stream, write_stream):
            async with ClientSession(read_stream, write_stream) as session:
                await session.initialize()
                
                # 调用爬虫工具
                result = await session.call_tool(
                    "crawl_taobao_products",
                    arguments={
                        'keyword': '蓝牙耳机',
                        'max_products': 1,
                        'max_reviews': 2,
                        'include_reviews': True,
                        'enable_debug': False
                    }
                )
                
                print("📊 MCP响应详情:")
                print(f"   响应类型: {type(result)}")
                print(f"   内容数量: {len(result.content) if result.content else 0}")
                
                if result.content:
                    for i, content in enumerate(result.content):
                        print(f"\n   内容 {i+1}:")
                        print(f"     类型: {type(content)}")
                        print(f"     属性: {dir(content)}")
                        
                        if hasattr(content, 'text'):
                            print(f"     文本长度: {len(content.text)}")
                            print(f"     文本前500字符:")
                            print(f"     {repr(content.text[:500])}")
                            
                            # 尝试解析JSON
                            try:
                                data = json.loads(content.text)
                                print(f"     ✅ JSON解析成功")
                                print(f"     JSON键: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                            except json.JSONDecodeError as e:
                                print(f"     ❌ JSON解析失败: {e}")
                                print(f"     原始文本: {content.text}")
                        
                        if hasattr(content, 'type'):
                            print(f"     内容类型: {content.type}")
                
                # 检查是否有结构化内容
                if hasattr(result, 'structuredContent') and result.structuredContent:
                    print(f"\n📋 结构化内容:")
                    print(f"   类型: {type(result.structuredContent)}")
                    print(f"   内容: {result.structuredContent}")
                
                # 检查是否有错误
                if hasattr(result, 'isError') and result.isError:
                    print(f"\n❌ 工具执行错误")
                
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_mcp_response())
