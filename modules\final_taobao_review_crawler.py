#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版淘宝商品评价爬虫
整合所有成功的优化策略
- 基于标签的评价分类提取
- 容器内滚动策略
- 真实评价验证
- 差评获取解决方案
"""

import time
import random
import pickle
import os
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from dataclasses import dataclass, field
from typing import List, Optional, Dict
import re

@dataclass
class ReviewInfo:
    """评价信息"""
    review_text: str = ""
    rating: str = ""
    reviewer_name: str = ""
    review_date: str = ""
    review_type: str = ""  # 'good', 'bad', 'medium'
    tags: List[str] = field(default_factory=list)

@dataclass
class ProductInfo:
    """商品信息"""
    title: str = ""
    price: str = ""
    sales: str = ""
    shop: str = ""
    location: str = ""
    detail_url: str = ""
    good_reviews: List[ReviewInfo] = field(default_factory=list)
    bad_reviews: List[ReviewInfo] = field(default_factory=list)
    medium_reviews: List[ReviewInfo] = field(default_factory=list)

class FinalTaobaoReviewCrawler:
    """最终版淘宝评价爬虫"""
    
    def __init__(self, headless: bool = True, debug: bool = True):
        self.headless = headless
        self.debug = debug
        self.driver = None
        self.is_logged_in = False
        self.cookies_file = "taobao_cookies.pkl"
        
    def _create_driver(self):
        """创建WebDriver"""
        if self.driver:
            return self.driver
            
        options = Options()
        
        if self.headless:
            options.add_argument('--headless')
            
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--log-level=3')
        
        # 禁用图片和CSS加载以提高速度
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2,
            "profile.managed_default_content_settings.stylesheets": 2
        }
        options.add_experimental_option("prefs", prefs)
        
        user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        options.add_argument(f'--user-agent={user_agent}')
        
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        service = webdriver.chrome.service.Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=options)
        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return self.driver
        
    def login_with_cookies(self) -> bool:
        """使用cookies登录"""
        if not os.path.exists(self.cookies_file):
            if self.debug:
                print("❌ cookies文件不存在，请先运行登录程序")
            return False
            
        try:
            driver = self._create_driver()
            driver.get("https://www.taobao.com")
            time.sleep(1)
            
            with open(self.cookies_file, 'rb') as f:
                cookies = pickle.load(f)
                
            for cookie in cookies:
                try:
                    driver.add_cookie(cookie)
                except:
                    continue
                    
            driver.refresh()
            time.sleep(2)
            self.is_logged_in = True
            
            if self.debug:
                print("✅ 使用cookies登录成功")
            return True
            
        except Exception as e:
            if self.debug:
                print(f"❌ cookies登录失败: {e}")
            return False
            
    def search_products(self, keyword: str, max_results: int = 10) -> List[ProductInfo]:
        """搜索商品"""
        if self.debug:
            print(f"🔍 搜索商品: {keyword}")
            
        try:
            # 访问淘宝搜索页面
            search_url = f"https://s.taobao.com/search?q={keyword}"
            self.driver.get(search_url)
            time.sleep(3)
            
            products = []
            
            # 商品链接选择器
            link_selectors = [
                "a[href*='item.taobao.com']",
                "a[href*='detail.tmall.com']"
            ]
            
            for selector in link_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    
                    if self.debug:
                        print(f"策略 '{selector}': 找到 {len(elements)} 个元素")
                    
                    if elements:
                        products.extend(self._extract_products_from_elements(elements, max_results))
                        break
                        
                except Exception as e:
                    if self.debug:
                        print(f"策略 '{selector}' 失败: {e}")
                    continue
            
            if self.debug:
                print(f"✅ 找到 {len(products)} 个商品")
            
            return products[:max_results]
            
        except Exception as e:
            if self.debug:
                print(f"❌ 搜索商品失败: {e}")
            return []
            
    def _extract_products_from_elements(self, elements, max_results: int) -> List[ProductInfo]:
        """从元素中提取商品信息"""
        products = []
        processed_urls = set()
        
        if self.debug:
            print(f"   开始处理 {len(elements)} 个链接，目标提取 {max_results} 个商品")
        
        for element in elements:
            if len(products) >= max_results:
                break
                
            try:
                # 获取商品链接
                href = element.get_attribute('href')
                if not href or href in processed_urls:
                    continue
                    
                processed_urls.add(href)
                
                # 提取商品信息
                product = self._extract_product_info(element, href)
                if product and product.title:
                    products.append(product)
                    
                    if self.debug:
                        print(f"   ✅ 链接 {len(products)}: {product.title[:40]}... 价格:{product.price} 销量:{product.sales}")
                        
            except Exception as e:
                continue
        
        if self.debug:
            print(f"   处理完成: 检查了 {len(processed_urls)} 个链接，成功提取 {len(products)} 个商品")
        
        return products
        
    def _extract_product_info(self, element, href: str) -> Optional[ProductInfo]:
        """提取单个商品信息"""
        try:
            product = ProductInfo()
            product.detail_url = href
            
            # 查找父容器
            parent = element
            for _ in range(5):  # 最多向上查找5层
                try:
                    parent = parent.find_element(By.XPATH, "./..")
                    
                    # 提取标题
                    if not product.title:
                        title_elements = parent.find_elements(By.CSS_SELECTOR, "[class*='title'], [class*='Title']")
                        for title_elem in title_elements:
                            title_text = title_elem.text.strip()
                            if 10 < len(title_text) < 200:
                                product.title = title_text
                                break
                    
                    # 提取价格
                    if not product.price:
                        price_elements = parent.find_elements(By.CSS_SELECTOR, "[class*='price'], [class*='Price']")
                        for price_elem in price_elements:
                            price_text = price_elem.text.strip()
                            if '¥' in price_text or '元' in price_text:
                                product.price = price_text
                                break
                    
                    # 提取销量
                    if not product.sales:
                        sales_elements = parent.find_elements(By.CSS_SELECTOR, "[class*='deal'], [class*='sales'], [class*='sold']")
                        for sales_elem in sales_elements:
                            sales_text = sales_elem.text.strip()
                            if any(word in sales_text for word in ['人付款', '人购买', '笔', '件']):
                                product.sales = sales_text
                                break
                    
                    if product.title and product.price:
                        break
                        
                except:
                    break
            
            return product if product.title else None

        except Exception:
            return None

    def get_products_with_reviews(self, keyword: str, max_results: int = 10, max_reviews_per_product: int = 60) -> List[ProductInfo]:
        """获取商品信息并包含评价详情（最终优化版）"""
        if self.debug:
            print(f"🎯 最终版获取商品及评价信息: {keyword}")

        # 先获取商品列表
        basic_products = self.search_products(keyword, max_results)

        if not basic_products:
            print("❌ 未找到商品，无法获取评价")
            return []

        detailed_products = []

        print(f"📋 开始获取 {len(basic_products)} 个商品的评价信息...")

        for i, basic_product in enumerate(basic_products, 1):
            try:
                print(f"\n🔍 处理商品 {i}/{len(basic_products)}: {basic_product.title[:40]}...")

                # 使用基于标签的评价提取
                reviews = self._extract_reviews_by_tags(basic_product.detail_url, max_reviews_per_product)

                # 分类评价
                classified_reviews = self._classify_reviews(reviews)

                # 创建详细商品信息
                detailed_product = self._create_detailed_product_info(basic_product, classified_reviews)
                detailed_products.append(detailed_product)

                if self.debug:
                    total_reviews = len(classified_reviews['good']) + len(classified_reviews['bad']) + len(classified_reviews['medium'])
                    print(f"   ✅ 获取评价: 好评{len(classified_reviews['good'])}条, 差评{len(classified_reviews['bad'])}条, 中评{len(classified_reviews['medium'])}条, 总计{total_reviews}条")

                # 适中延迟，确保稳定性
                time.sleep(random.uniform(1, 2))

            except Exception as e:
                print(f"❌ 获取商品 {i} 的评价失败: {e}")
                continue

        print(f"\n✅ 完成！成功获取 {len(detailed_products)} 个商品的详细信息")
        return detailed_products

    def _extract_reviews_by_tags(self, product_url: str, max_reviews: int = 60) -> List[ReviewInfo]:
        """基于标签提取评价（最终优化版）"""
        if self.debug:
            print(f"   🏷️  基于标签提取评价...")

        try:
            # 访问商品页面
            self.driver.get(product_url)
            time.sleep(2)

            # 点击全部评价
            if not self._click_all_reviews():
                return []

            # 分析评价标签
            tag_categories = self._analyze_review_tags()

            # 基于标签分类提取评价
            all_reviews = self._extract_reviews_by_tag_categories(tag_categories, max_reviews)

            return all_reviews

        except Exception as e:
            if self.debug:
                print(f"   基于标签提取失败: {e}")
            return []

    def _click_all_reviews(self) -> bool:
        """点击全部评价"""
        try:
            selectors = [
                "//div[contains(text(), '全部评价')]",
                "[class*='ShowButton']"
            ]

            for selector in selectors:
                try:
                    if selector.startswith("//"):
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for elem in elements:
                        if "全部评价" in elem.text:
                            self.driver.execute_script("arguments[0].click();", elem)
                            time.sleep(2)
                            if self.debug:
                                print(f"   ✅ 点击'全部评价'成功")
                            return True

                except Exception:
                    continue

            return False

        except Exception:
            return False

    def _analyze_review_tags(self) -> Dict[str, List]:
        """分析评价标签"""
        try:
            if self.debug:
                print(f"   📊 分析评价标签...")

            # 查找所有评价标签
            tag_elements = self.driver.find_elements(By.CSS_SELECTOR, "[class*='imprItem']")

            tag_categories = {
                'positive': [],
                'negative': [],
                'neutral': []
            }

            for elem in tag_elements:
                try:
                    if elem.is_displayed():
                        tag_text = elem.text.strip()

                        if tag_text:
                            # 分类标签
                            if self._is_positive_tag(tag_text):
                                tag_categories['positive'].append((tag_text, elem))
                            elif self._is_negative_tag(tag_text):
                                tag_categories['negative'].append((tag_text, elem))
                            else:
                                tag_categories['neutral'].append((tag_text, elem))

                except Exception:
                    continue

            if self.debug:
                print(f"   正面标签: {len(tag_categories['positive'])} 个")
                print(f"   负面标签: {len(tag_categories['negative'])} 个")
                print(f"   中性标签: {len(tag_categories['neutral'])} 个")

            return tag_categories

        except Exception as e:
            if self.debug:
                print(f"   标签分析失败: {e}")
            return {'positive': [], 'negative': [], 'neutral': []}

    def _is_positive_tag(self, tag_text: str) -> bool:
        """判断是否为正面标签"""
        # 先检查是否包含负面词汇
        if self._contains_negative_words(tag_text):
            return False

        positive_keywords = [
            # 直接正面词汇
            '好', '舒适', '清晰', '满意', '推荐', '不错', '优秀', '完美', '喜欢', '实用', '方便', '划算', '值得',
            # 能力/性能相关正面词汇
            '强', '大方', '容易', '多', '快', '稳定', '准确', '精确',
            # 功能相关正面词汇
            '续航强', '续航好', '续航能力强', '存电量多', '充电快',
            # 外观相关正面词汇
            '外观好看', '外观造型大方', '颜值高', '设计好',
            # 音频相关正面词汇
            '没杂音', '没电流声', '声音清晰', '音质清晰',
            # 使用相关正面词汇
            '打电话容易', '连接稳定', '操作简单', '佩戴感舒适'
        ]

        # 检查完整匹配
        if tag_text in positive_keywords:
            return True

        # 检查包含关系
        return any(keyword in tag_text for keyword in positive_keywords)

    def _is_negative_tag(self, tag_text: str) -> bool:
        """判断是否为负面标签"""
        negative_keywords = [
            # 直接负面词汇
            '差', '不好', '慢', '问题', '失望', '难', '坏', '糟糕', '后悔', '不值', '垃圾', '假', '骗', '退货',
            # 效果不好
            '效果不好', '佩戴效果不好', '音质不好', '质量不好',
            # 功能问题
            '续航差', '续航不好', '充电慢', '容易坏', '不耐用',
            # 使用问题
            '不舒适', '不方便', '难用', '复杂',
            # 音频问题
            '有杂音', '有电流声', '声音小', '音质差',
            # 连接问题
            '连接不稳定', '经常断连', '配对困难'
        ]

        return any(keyword in tag_text for keyword in negative_keywords)

    def _contains_negative_words(self, tag_text: str) -> bool:
        """检查是否包含负面词汇"""
        negative_words = ['不好', '不', '差', '难', '慢', '坏', '小']
        return any(word in tag_text for word in negative_words)

    def _extract_reviews_by_tag_categories(self, tag_categories: Dict[str, List], max_reviews: int) -> List[ReviewInfo]:
        """基于标签分类提取评价（增强鲁棒性）"""
        try:
            all_reviews = []

            # 检查是否有标签
            total_tags = len(tag_categories['positive']) + len(tag_categories['negative']) + len(tag_categories['neutral'])

            if total_tags == 0:
                if self.debug:
                    print(f"   ⚠️  未找到评价标签，使用备用策略：直接滚动提取")
                return self._extract_reviews_without_tags(max_reviews)

            # 为每个分类分配目标数量
            target_per_category = max_reviews // 3

            categories = [
                ('positive', tag_categories['positive'], 'good'),
                ('negative', tag_categories['negative'], 'bad'),
                ('neutral', tag_categories['neutral'], 'medium')
            ]

            for category_name, tags, review_type in categories:
                if self.debug:
                    print(f"   🏷️  处理{category_name}标签...")

                category_reviews = []

                # 尝试点击该分类的标签
                for tag_text, tag_elem in tags[:2]:  # 最多尝试2个标签
                    try:
                        if self.debug:
                            print(f"     点击标签: {tag_text}")

                        # 点击标签
                        self.driver.execute_script("arguments[0].click();", tag_elem)
                        time.sleep(2)

                        # 充分滚动加载评价
                        self._scroll_for_reviews()

                        # 提取当前显示的评价
                        current_reviews = self._extract_current_reviews(target_per_category)

                        # 标记评价类型和标签
                        for review in current_reviews:
                            review.review_type = review_type
                            review.tags.append(tag_text)

                        category_reviews.extend(current_reviews)

                        if len(category_reviews) >= target_per_category:
                            break

                    except Exception as e:
                        if self.debug:
                            print(f"     标签点击失败: {e}")
                        continue

                # 去重并限制数量
                unique_reviews = self._deduplicate_reviews(category_reviews)
                all_reviews.extend(unique_reviews[:target_per_category])

                if self.debug:
                    print(f"     ✅ {category_name}提取到 {len(unique_reviews[:target_per_category])} 条评价")

            # 如果基于标签的提取结果很少，使用备用策略补充
            if len(all_reviews) < max_reviews * 0.3:  # 如果获取的评价少于目标的30%
                if self.debug:
                    print(f"   ⚠️  标签策略获取评价较少({len(all_reviews)}条)，启用备用策略补充")

                backup_reviews = self._extract_reviews_without_tags(max_reviews - len(all_reviews))

                # 合并备用策略的结果，避免重复
                for backup_review in backup_reviews:
                    if not self._is_duplicate_review(backup_review, all_reviews):
                        all_reviews.append(backup_review)

            return all_reviews

        except Exception as e:
            if self.debug:
                print(f"   标签分类提取失败: {e}")
            # 失败时使用备用策略
            return self._extract_reviews_without_tags(max_reviews)

    def _extract_reviews_without_tags(self, max_reviews: int) -> List[ReviewInfo]:
        """无标签时的备用策略：直接滚动提取评价"""
        try:
            if self.debug:
                print(f"   🔄 启用备用策略：直接滚动提取评价")

            # 充分滚动加载评价
            self._scroll_for_reviews_extensively()

            # 提取所有可见的评价
            all_reviews = self._extract_all_visible_reviews(max_reviews)

            # 智能分类评价（基于内容而非标签）
            classified_reviews = self._classify_reviews_by_content(all_reviews)

            if self.debug:
                print(f"   🔄 备用策略提取到 {len(all_reviews)} 条评价")

            return all_reviews

        except Exception as e:
            if self.debug:
                print(f"   备用策略失败: {e}")
            return []

    def _scroll_for_reviews_extensively(self):
        """更充分的滚动策略（用于无标签情况）"""
        try:
            if self.debug:
                print(f"     开始更充分的滚动加载...")

            # 查找评价容器
            container = self._find_review_container()

            if container:
                # 在容器内更充分滚动50次
                scroll_count = 50
                if self.debug:
                    print(f"     在评价容器内滚动 {scroll_count} 次")

                for i in range(scroll_count):
                    if self.debug and (i + 1) % 15 == 0:
                        print(f"     容器内滚动 {i + 1}/{scroll_count}")

                    self.driver.execute_script("arguments[0].scrollTop += 300;", container)
                    time.sleep(0.1)

                # 滚动到容器底部
                self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight;", container)
                time.sleep(2)

                # 滚动回容器顶部
                self.driver.execute_script("arguments[0].scrollTop = 0;", container)
                time.sleep(1)

                if self.debug:
                    print(f"     ✅ 充分滚动完成")
            else:
                # 页面滚动备用方案
                scroll_count = 30
                if self.debug:
                    print(f"     使用页面滚动，滚动 {scroll_count} 次")

                for i in range(scroll_count):
                    if self.debug and (i + 1) % 10 == 0:
                        print(f"     页面滚动 {i + 1}/{scroll_count}")

                    self.driver.execute_script("window.scrollBy(0, 400);")
                    time.sleep(0.1)

        except Exception as e:
            if self.debug:
                print(f"     充分滚动失败: {e}")

    def _find_review_container(self):
        """查找评价容器"""
        try:
            container_selectors = [
                "[class*='comment']",
                "[class*='review']",
                "[class*='content']",
                "[class*='rate']",
                "[class*='evaluation']"
            ]

            for selector in container_selectors:
                try:
                    containers = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for c in containers:
                        if c.is_displayed() and c.size['height'] > 200:
                            return c
                except:
                    continue

            return None

        except Exception:
            return None

    def _extract_all_visible_reviews(self, max_reviews: int) -> List[ReviewInfo]:
        """提取所有可见的评价（无标签策略）"""
        try:
            reviews = []

            if self.debug:
                print(f"     开始提取所有可见评价，目标 {max_reviews} 条")

            # 更广泛的评价选择器
            review_selectors = [
                # 评价内容相关
                "[class*='reviewContent']",
                "[class*='comment-content']",
                "[class*='review-text']",
                "[class*='user-comment']",
                "[class*='content']",
                "[class*='text']",
                # 评价项目
                "[class*='item']",
                "[class*='review-item']",
                "[class*='comment-item']"
            ]

            # 更宽松的评价关键词
            review_keywords = [
                '质量', '音质', '续航', '佩戴', '舒适', '满意', '不错', '好用',
                '快递', '包装', '客服', '推荐', '喜欢', '实用', '方便',
                '差', '不好', '失望', '问题', '后悔', '不值',
                '收到', '买', '用', '听', '戴', '感觉', '体验', '效果',
                '服务', '发货', '物流', '价格', '性价比'
            ]

            for selector in review_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for elem in elements:
                        try:
                            if elem.is_displayed():
                                text = elem.text.strip()

                                # 更宽松的评价验证（适应评价较少的商品）
                                if (20 < len(text) < 500 and  # 放宽长度限制
                                    self._is_potential_review(text) and  # 更宽松的验证
                                    any(keyword in text for keyword in review_keywords)):  # 包含评价关键词

                                    review = ReviewInfo()
                                    review.review_text = text
                                    self._extract_additional_info(elem, review)

                                    # 检查是否重复
                                    if not self._is_duplicate_review(review, reviews):
                                        reviews.append(review)

                                        if self.debug and len(reviews) <= 5:
                                            print(f"     提取评价 {len(reviews)}: {text[:50]}...")

                                        if len(reviews) >= max_reviews:
                                            break
                        except:
                            continue

                    if len(reviews) >= max_reviews:
                        break

                except:
                    continue

            if self.debug:
                print(f"     ✅ 提取到 {len(reviews)} 条可见评价")

            return reviews

        except Exception as e:
            if self.debug:
                print(f"     提取可见评价失败: {e}")
            return []

    def _is_potential_review(self, text: str) -> bool:
        """检查是否是潜在的评价（更宽松的验证）"""
        # 排除明显的非评价内容
        exclude_patterns = [
            '立即购买', '加入购物车', '收藏', '分享', '店铺', '客服',
            '规格参数', '商品详情', '配送', '保障', '支付方式',
            '颜色分类', '数量', '有货', '限购', '优惠', '活动'
        ]

        # 如果包含太多非评价词汇，可能不是评价
        exclude_count = sum(1 for pattern in exclude_patterns if pattern in text)
        if exclude_count >= 2:
            return False

        # 检查是否包含评价特征（更宽松）
        review_indicators = [
            '买', '用', '听', '戴', '感觉', '觉得', '认为', '体验', '效果',
            '收到', '拿到', '试用', '使用', '购买', '入手', '不错', '满意',
            '好', '差', '质量', '服务', '快递', '包装', '推荐', '值得'
        ]

        return any(indicator in text for indicator in review_indicators)

    def _classify_reviews_by_content(self, reviews: List[ReviewInfo]) -> List[ReviewInfo]:
        """基于内容智能分类评价（无标签时使用）"""
        try:
            for review in reviews:
                if not review.review_type:  # 如果还没有分类
                    # 基于内容关键词分类
                    text = review.review_text.lower()

                    # 正面关键词
                    positive_words = [
                        '好', '不错', '满意', '推荐', '喜欢', '值得', '优秀', '完美',
                        '质量好', '服务好', '快递快', '包装好', '性价比高'
                    ]

                    # 负面关键词
                    negative_words = [
                        '差', '不好', '失望', '后悔', '不值', '垃圾', '坏',
                        '质量差', '服务差', '慢', '问题', '不满意'
                    ]

                    positive_count = sum(1 for word in positive_words if word in text)
                    negative_count = sum(1 for word in negative_words if word in text)

                    if positive_count > negative_count:
                        review.review_type = 'good'
                    elif negative_count > positive_count:
                        review.review_type = 'bad'
                    else:
                        review.review_type = 'medium'

            return reviews

        except Exception as e:
            if self.debug:
                print(f"   内容分类失败: {e}")
            return reviews

    def _extract_all_visible_reviews(self, max_reviews: int) -> List[ReviewInfo]:
        """提取所有可见的评价（无标签策略）"""
        try:
            reviews = []

            if self.debug:
                print(f"     开始提取所有可见评价，目标 {max_reviews} 条")

            # 更广泛的评价选择器
            review_selectors = [
                # 评价内容相关
                "[class*='reviewContent']",
                "[class*='comment-content']",
                "[class*='review-text']",
                "[class*='user-comment']",
                "[class*='content']",
                "[class*='text']",
                # 评价项目
                "[class*='item']",
                "[class*='review-item']",
                "[class*='comment-item']"
            ]

            # 更宽松的评价关键词
            review_keywords = [
                '质量', '音质', '续航', '佩戴', '舒适', '满意', '不错', '好用',
                '快递', '包装', '客服', '推荐', '喜欢', '实用', '方便',
                '差', '不好', '失望', '问题', '后悔', '不值',
                '收到', '买', '用', '听', '戴', '感觉', '体验', '效果',
                '服务', '发货', '物流', '价格', '性价比'
            ]

            for selector in review_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for elem in elements:
                        try:
                            if elem.is_displayed():
                                text = elem.text.strip()

                                # 更宽松的评价验证（适应评价较少的商品）
                                if (20 < len(text) < 500 and  # 放宽长度限制
                                    self._is_potential_review(text) and  # 更宽松的验证
                                    any(keyword in text for keyword in review_keywords)):  # 包含评价关键词

                                    review = ReviewInfo()
                                    review.review_text = text
                                    self._extract_additional_info(elem, review)

                                    # 检查是否重复
                                    if not self._is_duplicate_review(review, reviews):
                                        reviews.append(review)

                                        if self.debug and len(reviews) <= 5:
                                            print(f"     提取评价 {len(reviews)}: {text[:50]}...")

                                        if len(reviews) >= max_reviews:
                                            break
                        except:
                            continue

                    if len(reviews) >= max_reviews:
                        break

                except:
                    continue

            if self.debug:
                print(f"     ✅ 提取到 {len(reviews)} 条可见评价")

            return reviews

        except Exception as e:
            if self.debug:
                print(f"     提取可见评价失败: {e}")
            return []

    def _is_potential_review(self, text: str) -> bool:
        """检查是否是潜在的评价（更宽松的验证）"""
        # 排除明显的非评价内容
        exclude_patterns = [
            '立即购买', '加入购物车', '收藏', '分享', '店铺', '客服',
            '规格参数', '商品详情', '配送', '保障', '支付方式',
            '颜色分类', '数量', '有货', '限购', '优惠', '活动'
        ]

        # 如果包含太多非评价词汇，可能不是评价
        exclude_count = sum(1 for pattern in exclude_patterns if pattern in text)
        if exclude_count >= 2:
            return False

        # 检查是否包含评价特征（更宽松）
        review_indicators = [
            '买', '用', '听', '戴', '感觉', '觉得', '认为', '体验', '效果',
            '收到', '拿到', '试用', '使用', '购买', '入手', '不错', '满意',
            '好', '差', '质量', '服务', '快递', '包装', '推荐', '值得'
        ]

        return any(indicator in text for indicator in review_indicators)

    def _classify_reviews_by_content(self, reviews: List[ReviewInfo]) -> List[ReviewInfo]:
        """基于内容智能分类评价（无标签时使用）"""
        try:
            for review in reviews:
                if not review.review_type:  # 如果还没有分类
                    # 基于内容关键词分类
                    text = review.review_text.lower()

                    # 正面关键词
                    positive_words = [
                        '好', '不错', '满意', '推荐', '喜欢', '值得', '优秀', '完美',
                        '质量好', '服务好', '快递快', '包装好', '性价比高'
                    ]

                    # 负面关键词
                    negative_words = [
                        '差', '不好', '失望', '后悔', '不值', '垃圾', '坏',
                        '质量差', '服务差', '慢', '问题', '不满意'
                    ]

                    positive_count = sum(1 for word in positive_words if word in text)
                    negative_count = sum(1 for word in negative_words if word in text)

                    if positive_count > negative_count:
                        review.review_type = 'good'
                    elif negative_count > positive_count:
                        review.review_type = 'bad'
                    else:
                        review.review_type = 'medium'

            return reviews

        except Exception as e:
            if self.debug:
                print(f"   内容分类失败: {e}")
            return reviews

    def _scroll_for_reviews(self):
        """充分滚动加载评价"""
        try:
            if self.debug:
                print(f"     开始充分滚动加载评价...")

            # 查找评价容器
            container_selectors = [
                "[class*='comment']",
                "[class*='review']",
                "[class*='content']"
            ]

            container = None
            for selector in container_selectors:
                try:
                    containers = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for c in containers:
                        if c.is_displayed() and c.size['height'] > 200:
                            container = c
                            break
                    if container:
                        break
                except:
                    continue

            if container:
                # 在容器内充分滚动30次
                scroll_count = 30
                if self.debug:
                    print(f"     在评价容器内滚动 {scroll_count} 次")

                for i in range(scroll_count):
                    if self.debug and (i + 1) % 10 == 0:
                        print(f"     容器内滚动 {i + 1}/{scroll_count}")

                    self.driver.execute_script("arguments[0].scrollTop += 400;", container)
                    time.sleep(0.1)

                # 滚动到容器底部
                self.driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight;", container)
                time.sleep(1)

                # 滚动回容器顶部
                self.driver.execute_script("arguments[0].scrollTop = 0;", container)
                time.sleep(1)

                if self.debug:
                    print(f"     ✅ 容器内滚动完成")
            else:
                # 页面滚动备用方案
                scroll_count = 20
                if self.debug:
                    print(f"     使用页面滚动，滚动 {scroll_count} 次")

                for i in range(scroll_count):
                    self.driver.execute_script("window.scrollBy(0, 500);")
                    time.sleep(0.1)

        except Exception as e:
            if self.debug:
                print(f"     滚动失败: {e}")

    def _extract_current_reviews(self, target_count: int) -> List[ReviewInfo]:
        """提取当前显示的真实评价"""
        try:
            reviews = []

            if self.debug:
                print(f"     开始提取真实评价，目标 {target_count} 条")

            # 更精确的评价选择器
            review_selectors = [
                "[class*='reviewContent']",
                "[class*='comment-content']",
                "[class*='review-text']",
                "[class*='user-comment']",
                "[class*='content']",
                "[class*='text']"
            ]

            # 评价关键词
            review_keywords = [
                '质量', '音质', '续航', '佩戴', '舒适', '满意', '不错', '好用',
                '快递', '包装', '客服', '推荐', '喜欢', '实用', '方便',
                '差', '不好', '失望', '问题', '后悔', '不值'
            ]

            for selector in review_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for elem in elements:
                        try:
                            if elem.is_displayed():
                                text = elem.text.strip()

                                # 更严格的评价验证
                                if (30 < len(text) < 300 and  # 合理的评价长度
                                    self._is_genuine_review(text) and  # 真实评价检查
                                    any(keyword in text for keyword in review_keywords)):  # 包含评价关键词

                                    review = ReviewInfo()
                                    review.review_text = text
                                    self._extract_additional_info(elem, review)

                                    # 检查是否重复
                                    if not self._is_duplicate_review(review, reviews):
                                        reviews.append(review)

                                        if self.debug and len(reviews) <= 3:
                                            print(f"     提取评价 {len(reviews)}: {text[:40]}...")

                                        if len(reviews) >= target_count:
                                            break
                        except:
                            continue

                    if len(reviews) >= target_count:
                        break

                except:
                    continue

            if self.debug:
                print(f"     ✅ 提取到 {len(reviews)} 条真实评价")

            return reviews

        except Exception as e:
            if self.debug:
                print(f"     提取评价失败: {e}")
            return []

    def _is_genuine_review(self, text: str) -> bool:
        """检查是否是真实评价"""
        # 排除商品描述和广告
        exclude_patterns = [
            '华强北', '蓝牙耳机', '适用苹果', '新款', '官方正品', '品牌补贴',
            '数量有限', '超越版', '原版', '全功能', '人评价', '回头客',
            '【', '】', '★', '补贴', '%', '前100名', '下单'
        ]

        # 如果包含太多商品描述词汇，可能不是真实评价
        exclude_count = sum(1 for pattern in exclude_patterns if pattern in text)
        if exclude_count >= 3:
            return False

        # 检查是否包含评价特征
        review_indicators = [
            '买', '用', '听', '戴', '感觉', '觉得', '认为', '体验', '效果',
            '收到', '拿到', '试用', '使用', '购买', '入手'
        ]

        return any(indicator in text for indicator in review_indicators)

    def _is_duplicate_review(self, review: ReviewInfo, existing_reviews: List[ReviewInfo]) -> bool:
        """检查评价是否重复"""
        text_key = review.review_text[:50]
        for existing in existing_reviews:
            if text_key == existing.review_text[:50]:
                return True
        return False

    def _extract_additional_info(self, element, review: ReviewInfo):
        """提取额外信息（用户名、日期等）"""
        try:
            parent = element.find_element(By.XPATH, "./..")
            user_elements = parent.find_elements(By.XPATH, ".//*")

            for elem in user_elements:
                text = elem.text.strip()
                if 2 < len(text) < 20 and '*' in text:
                    review.reviewer_name = text
                    break

        except:
            pass

    def _deduplicate_reviews(self, reviews: List[ReviewInfo]) -> List[ReviewInfo]:
        """去重"""
        unique_reviews = []
        seen_texts = set()

        for review in reviews:
            text_key = review.review_text[:50]
            if text_key not in seen_texts:
                seen_texts.add(text_key)
                unique_reviews.append(review)

        return unique_reviews

    def _classify_reviews(self, reviews: List[ReviewInfo]) -> Dict[str, List[ReviewInfo]]:
        """分类评价"""
        classified = {'good': [], 'bad': [], 'medium': []}

        for review in reviews:
            if review.review_type == 'good':
                classified['good'].append(review)
            elif review.review_type == 'bad':
                classified['bad'].append(review)
            else:
                classified['medium'].append(review)

        return classified

    def _create_detailed_product_info(self, basic_product: ProductInfo, classified_reviews: Dict[str, List[ReviewInfo]]) -> ProductInfo:
        """创建详细商品信息"""
        detailed_product = ProductInfo()

        # 复制基本信息
        detailed_product.title = basic_product.title
        detailed_product.price = basic_product.price
        detailed_product.sales = basic_product.sales
        detailed_product.shop = basic_product.shop
        detailed_product.location = basic_product.location
        detailed_product.detail_url = basic_product.detail_url

        # 添加分类评价
        detailed_product.good_reviews = classified_reviews['good']
        detailed_product.bad_reviews = classified_reviews['bad']
        detailed_product.medium_reviews = classified_reviews['medium']

        return detailed_product

    def save_results(self, products: List[ProductInfo], keyword: str) -> str:
        """保存结果到JSON文件"""
        try:
            output_dir = r"c:\Users\<USER>\Desktop\dify_test\modules\output"
            os.makedirs(output_dir, exist_ok=True)

            # 准备数据
            data = {
                "crawler_version": "最终优化版",
                "search_keyword": keyword,
                "crawl_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "total_products": len(products),
                "products": []
            }

            total_reviews = 0
            total_good = 0
            total_bad = 0
            total_medium = 0

            for product in products:
                good_count = len(product.good_reviews)
                bad_count = len(product.bad_reviews)
                medium_count = len(product.medium_reviews)

                total_reviews += good_count + bad_count + medium_count
                total_good += good_count
                total_bad += bad_count
                total_medium += medium_count

                product_data = {
                    "title": product.title,
                    "price": product.price,
                    "sales": product.sales,
                    "shop": product.shop,
                    "location": product.location,
                    "detail_url": product.detail_url,
                    "review_summary": {
                        "good_count": good_count,
                        "bad_count": bad_count,
                        "medium_count": medium_count,
                        "total_count": good_count + bad_count + medium_count
                    },
                    "reviews": {
                        "good_reviews": [
                            {
                                "text": review.review_text,
                                "reviewer": review.reviewer_name,
                                "date": review.review_date,
                                "tags": review.tags
                            }
                            for review in product.good_reviews
                        ],
                        "bad_reviews": [
                            {
                                "text": review.review_text,
                                "reviewer": review.reviewer_name,
                                "date": review.review_date,
                                "tags": review.tags
                            }
                            for review in product.bad_reviews
                        ],
                        "medium_reviews": [
                            {
                                "text": review.review_text,
                                "reviewer": review.reviewer_name,
                                "date": review.review_date,
                                "tags": review.tags
                            }
                            for review in product.medium_reviews
                        ]
                    }
                }

                data["products"].append(product_data)

            # 添加总体统计
            data["summary"] = {
                "total_reviews": total_reviews,
                "good_reviews": total_good,
                "bad_reviews": total_bad,
                "medium_reviews": total_medium,
                "bad_review_rate": f"{total_bad/total_reviews*100:.1f}%" if total_reviews > 0 else "0%"
            }

            # 保存文件
            timestamp = int(time.time())
            filename = f"final_taobao_reviews_{keyword}_{timestamp}.json"
            filepath = os.path.join(output_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            print(f"💾 详细评价结果已保存到: {filepath}")
            print(f"📊 共保存 {len(products)} 个商品的评价信息")

            return filepath

        except Exception as e:
            print(f"保存结果失败: {e}")
            return ""

    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            self.driver = None
