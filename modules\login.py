#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
淘宝登录脚本
============

用于获取cookies供爬虫使用
请在浏览器中手动完成登录操作
"""

import time
import pickle
import os
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

def login_taobao():
    """淘宝登录获取cookies"""
    print("🔐 淘宝登录程序")
    print("=" * 40)
    print("请在打开的浏览器中手动登录淘宝")
    print("登录成功后程序会自动保存cookies")
    print()
    
    try:
        # 创建浏览器
        options = Options()
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--window-size=1200,800')
        
        user_agent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        options.add_argument(f'--user-agent={user_agent}')
        
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        service = webdriver.chrome.service.Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("🌐 正在打开淘宝登录页面...")
        driver.get("https://login.taobao.com")
        
        print("👤 请在浏览器中完成登录操作")
        print("   1. 输入用户名和密码")
        print("   2. 完成验证码验证")
        print("   3. 确保登录成功")
        print()
        
        # 等待用户登录
        while True:
            try:
                # 检查是否登录成功
                current_url = driver.current_url
                
                if "login.taobao.com" not in current_url:
                    # 访问淘宝首页确认登录状态
                    driver.get("https://www.taobao.com")
                    time.sleep(3)
                    
                    # 检查登录状态
                    page_source = driver.page_source
                    if "登录" not in page_source or "亲" in page_source:
                        print("✅ 检测到登录成功！")
                        break
                
                print("⏳ 等待登录完成...")
                time.sleep(3)
                
            except Exception as e:
                print(f"检查登录状态时出错: {e}")
                time.sleep(3)
        
        # 保存cookies
        print("💾 正在保存cookies...")
        cookies = driver.get_cookies()
        
        with open("taobao_cookies.pkl", 'wb') as f:
            pickle.dump(cookies, f)
        
        print("✅ cookies保存成功！")
        print("📁 文件位置: taobao_cookies.pkl")
        print()
        print("🎉 登录完成！现在可以运行爬虫了")
        print("下一步: python run_final_crawler.py")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return False

def check_cookies():
    """检查cookies是否存在"""
    if os.path.exists("taobao_cookies.pkl"):
        print("✅ 发现cookies文件")
        
        # 检查文件修改时间
        mtime = os.path.getmtime("taobao_cookies.pkl")
        import datetime
        mod_time = datetime.datetime.fromtimestamp(mtime)
        
        print(f"📅 创建时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 检查是否过期（7天）
        now = datetime.datetime.now()
        days_old = (now - mod_time).days
        
        if days_old > 7:
            print(f"⚠️  cookies已过期 ({days_old}天前创建)")
            print("建议重新登录获取新的cookies")
            return False
        else:
            print(f"✅ cookies有效 ({days_old}天前创建)")
            return True
    else:
        print("❌ 未找到cookies文件")
        print("请先运行登录程序")
        return False

def main():
    """主函数"""
    print("🔐 淘宝登录管理工具")
    print("=" * 40)
    
    print("选择操作:")
    print("1. 登录淘宝获取cookies")
    print("2. 检查cookies状态")
    print("3. 退出")
    
    choice = input("请选择 (1-3): ")
    
    if choice == "1":
        if login_taobao():
            print("\n🎯 下一步:")
            print("运行 python run_final_crawler.py 开始爬取")
    elif choice == "2":
        if check_cookies():
            print("\n🎯 cookies有效，可以直接使用爬虫")
            print("运行 python run_final_crawler.py 开始爬取")
        else:
            print("\n🎯 请先登录获取cookies")
    elif choice == "3":
        print("👋 再见！")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
