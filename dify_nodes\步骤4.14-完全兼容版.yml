app:
  description: 🛍️ 电商爆品选品专家 - 多平台智能分析系统，支持抖音、淘宝、亚马逊、拼多多的爆品选品，提供优缺点分析和需求洞察
  icon: 🛍️
  icon_background: '#FF6B35'
  mode: advanced-chat
  name: 电商爆品选品专家-集成版
  use_icon_as_answer_icon: false

dependencies: []
kind: app
version: 0.3.0

workflow:
  conversation_variables: []
  environment_variables:
  - description: 🎯 MCP淘宝爬虫服务器路径
    id: c81c1ea2-4841-4022-892b-5bf4e3fda1c9
    name: TAOBAO_MCP_SERVER_PATH
    selector:
    - env
    - TAOBAO_MCP_SERVER_PATH
    value: c:\Users\<USER>\Desktop\dify_test\modules\taobao_mcp_server.py
    value_type: string
  - description: 🐍 Python解释器路径
    id: d92d2fb3-5952-4133-993c-6cf5f4eda2ca
    name: PYTHON_PATH
    selector:
    - env
    - PYTHON_PATH
    value: python
    value_type: string
  
  features:
    file_upload:
      enabled: false
    opening_statement: |
      🎯 欢迎使用电商爆品选品专家！我是您的专业选品顾问，专门帮助您在抖音、淘宝、亚马逊、拼多多四大平台发现爆品机会。

      🎉 **重大升级 - MCP协议集成**：
      ✅ 集成MCP标准协议淘宝爬虫 - 差评获取率提升到33.3%
      ✅ 智能关键词提取 - 支持自然语言输入
      ✅ 94.4%真实评价率 - 高质量数据分析
      ✅ Model Context Protocol - 标准化AI工具接口

      🗣️ **自然输入方式**：
      您可以用自然语言描述您的需求，比如：
      • "我想分析蓝牙耳机的市场机会"
      • "帮我看看运动健身类产品怎么样"
      • "手机壳好卖吗？"
      • "分析一下充电器的选品机会"

      我的智能关键词提取器会自动识别您要分析的商品，然后为您生成详细的多平台选品分析报告！
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - 我想分析蓝牙耳机的市场机会
    - 帮我看看运动健身类产品怎么样
    - 手机壳好卖吗？
    - 分析一下充电器的选品机会
    - 美妆护肤产品的市场潜力如何？
    suggested_questions_after_answer:
      enabled: true
    text_to_speech:
      enabled: false

  graph:
    edges:
    # Start 到关键词提取的连接
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: start-source-keyword_extractor-target
      source: start
      sourceHandle: source
      target: keyword_extractor
      targetHandle: target
      type: custom

    # 关键词提取到四个搜索工具的连接
    - data:
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: keyword_extractor-source-douyin_search-target
      source: keyword_extractor
      sourceHandle: source
      target: douyin_search
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: keyword_extractor-source-taobao_search-target
      source: keyword_extractor
      sourceHandle: source
      target: taobao_search
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: keyword_extractor-source-amazon_search-target
      source: keyword_extractor
      sourceHandle: source
      target: amazon_search
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: keyword_extractor-source-pdd_search-target
      source: keyword_extractor
      sourceHandle: source
      target: pdd_search
      targetHandle: target
      type: custom
    
    # 四个搜索工具到结构化分析的连接
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: douyin_search-source-structured_analysis-target
      source: douyin_search
      sourceHandle: source
      target: structured_analysis
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: taobao_search-source-structured_analysis-target
      source: taobao_search
      sourceHandle: source
      target: structured_analysis
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: amazon_search-source-structured_analysis-target
      source: amazon_search
      sourceHandle: source
      target: structured_analysis
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: pdd_search-source-structured_analysis-target
      source: pdd_search
      sourceHandle: source
      target: structured_analysis
      targetHandle: target
      type: custom
    
    # 结构化分析到变量计算的连接
    - data:
        isInLoop: false
        sourceType: llm
        targetType: assigner
      id: structured_analysis-source-score_calculator-target
      source: structured_analysis
      sourceHandle: source
      target: score_calculator
      targetHandle: target
      type: custom
    
    # 变量计算到格式化输出的连接
    - data:
        isInLoop: false
        sourceType: assigner
        targetType: llm
      id: score_calculator-source-format_output-target
      source: score_calculator
      sourceHandle: source
      target: format_output
      targetHandle: target
      type: custom
    
    # 格式化输出到Answer的连接
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: format_output-source-final_answer-target
      source: format_output
      sourceHandle: source
      target: final_answer
      targetHandle: target
      type: custom

    nodes:
    # 开始节点
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 您想分析什么商品？
          max_length: 512
          options: []
          required: true
          type: text-input
          variable: user_input
      height: 116
      id: start
      position:
        x: -700
        y: 300
      positionAbsolute:
        x: -700
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 🧠 智能关键词提取器
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: '智能解析用户输入，提取商品关键词和分类信息'
        model:
          completion_params:
            temperature: 0.1
            max_tokens: 200
          mode: chat
          name: gemini-2.5-pro
          provider: gemini
        prompt_template:
        - role: system
          text: |
            你是一个专业的商品关键词提取专家。你的任务是从用户的自然语言输入中提取出准确的商品关键词。

            🎯 **提取规则：**
            1. 提取核心商品名称，去除修饰词
            2. 保持2-6个字的长度
            3. 使用标准的商品类目名称
            4. 避免过于宽泛或过于具体

            📝 **输出格式：**
            只输出提取的关键词，不要任何解释或额外文字。

            🌰 **示例：**
            - 输入："我想分析蓝牙耳机的市场机会" → 输出："蓝牙耳机"
            - 输入："帮我看看运动健身类产品怎么样" → 输出："运动健身"
            - 输入："手机壳好卖吗" → 输出："手机壳"
            - 输入："分析一下充电器的选品机会" → 输出："充电器"
            - 输入："美妆护肤产品的市场潜力" → 输出："美妆护肤"
        - role: user
          text: |
            请从以下用户输入中提取商品关键词：

            用户输入：{{#start.user_input#}}

            请只输出提取的关键词，不要任何其他内容。
        selected: false
        structured_output:
          schema:
            type: object
            properties:
              keyword:
                type: string
                description: 提取的商品关键词
              category:
                type: string
                description: 商品分类
              confidence:
                type: number
                minimum: 0
                maximum: 1
                description: 提取置信度
            required: ["keyword", "category", "confidence"]
        structured_output_enabled: true
        title: 🧠 智能关键词提取器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 98
      id: keyword_extractor
      position:
        x: -550
        y: 300
      positionAbsolute:
        x: -550
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 抖音搜索工具
    - data:
        desc: ''
        provider_id: builtin
        provider_name: builtin
        provider_type: builtin
        selected: false
        title: 抖音数据采集
        tool_configurations: {}
        tool_label: HTTP Request
        tool_name: http_request
        tool_parameters:
          url:
            type: constant
            value: "https://httpbin.org/json"
          method:
            type: constant
            value: "GET"
          headers:
            type: constant
            value: "Content-Type: application/json\nPlatform: Douyin\nCategory: {{#keyword_extractor.structured_output.keyword#}}"
        type: tool
      height: 148
      id: douyin_search
      position:
        x: -400
        y: 80
      positionAbsolute:
        x: -400
        y: 80
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # MCP淘宝爬虫工具
    - data:
        desc: '使用Model Context Protocol标准接口调用淘宝爬虫'
        provider_id: builtin
        provider_name: builtin
        provider_type: builtin
        selected: false
        title: 🎯 MCP淘宝爬虫服务器
        tool_configurations: {}
        tool_label: Code Execution
        tool_name: code
        tool_parameters:
          code:
            type: constant
            value: |
              import asyncio
              import json
              import sys
              import os

              # 添加模块路径
              sys.path.append(r'c:\Users\<USER>\Desktop\dify_test\modules')

              from mcp import ClientSession, StdioServerParameters
              from mcp.client.stdio import stdio_client

              async def call_mcp_crawler():
                  """调用MCP淘宝爬虫"""
                  try:
                      # 从环境变量获取配置
                      server_path = r"{{#env.TAOBAO_MCP_SERVER_PATH#}}"
                      python_path = "{{#env.PYTHON_PATH#}}"

                      # 提取参数
                      keyword = "{{#keyword_extractor.structured_output.keyword#}}"
                      category = "{{#keyword_extractor.structured_output.category#}}"
                      confidence = {{#keyword_extractor.structured_output.confidence#}}

                      # 创建MCP客户端参数
                      server_params = StdioServerParameters(
                          command=python_path,
                          args=[server_path],
                          env=os.environ.copy()
                      )

                      # 连接MCP服务器并调用工具
                      async with stdio_client(server_params) as (read_stream, write_stream):
                          async with ClientSession(read_stream, write_stream) as session:
                              # 初始化连接
                              await session.initialize()

                              # 调用爬虫工具
                              result = await session.call_tool(
                                  "crawl_taobao_products",
                                  arguments={
                                      'keyword': keyword,
                                      'max_products': 3,
                                      'max_reviews': 10,
                                      'include_reviews': True,
                                      'enable_debug': False
                                  }
                              )

                              # 解析结果
                              if result.content:
                                  content = result.content[0]
                                  if hasattr(content, 'text'):
                                      try:
                                          data = json.loads(content.text)
                                          return {
                                              "success": True,
                                              "platform": "taobao",
                                              "keyword": keyword,
                                              "category": category,
                                              "confidence": confidence,
                                              "mcp_data": data,
                                              "source": "MCP Protocol v1.12.2"
                                          }
                                      except json.JSONDecodeError:
                                          return {
                                              "success": False,
                                              "error": "MCP返回数据格式错误",
                                              "raw_content": content.text[:500]
                                          }

                              return {"success": False, "error": "MCP无返回内容"}

                  except Exception as e:
                      return {
                          "success": False,
                          "error": f"MCP调用失败: {str(e)}",
                          "platform": "taobao"
                      }

              # 执行异步调用
              result = asyncio.run(call_mcp_crawler())
              print(json.dumps(result, ensure_ascii=False, indent=2))
        type: tool
      height: 148
      id: taobao_search
      position:
        x: -400
        y: 250
      positionAbsolute:
        x: -400
        y: 250
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 亚马逊搜索工具
    - data:
        desc: ''
        provider_id: builtin
        provider_name: builtin
        provider_type: builtin
        selected: false
        title: 亚马逊数据采集
        tool_configurations: {}
        tool_label: HTTP Request
        tool_name: http_request
        tool_parameters:
          url:
            type: constant
            value: "https://httpbin.org/json"
          method:
            type: constant
            value: "GET"
          headers:
            type: constant
            value: "Content-Type: application/json\nPlatform: Amazon\nCategory: {{#keyword_extractor.structured_output.keyword#}}"
        type: tool
      height: 148
      id: amazon_search
      position:
        x: -400
        y: 420
      positionAbsolute:
        x: -400
        y: 420
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 拼多多搜索工具
    - data:
        desc: ''
        provider_id: builtin
        provider_name: builtin
        provider_type: builtin
        selected: false
        title: 拼多多数据采集
        tool_configurations: {}
        tool_label: HTTP Request
        tool_name: http_request
        tool_parameters:
          url:
            type: constant
            value: "https://httpbin.org/json"
          method:
            type: constant
            value: "GET"
          headers:
            type: constant
            value: "Content-Type: application/json\nPlatform: Pinduoduo\nCategory: {{#keyword_extractor.structured_output.keyword#}}"
        type: tool
      height: 148
      id: pdd_search
      position:
        x: -400
        y: 590
      positionAbsolute:
        x: -400
        y: 590
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 智能分析引擎（保持步骤4.6的完整功能）
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.2
            max_tokens: 2048
          mode: chat
          name: gemini-2.5-pro
          provider: openai
        prompt_template:
        - role: system
          text: |
            你是一位资深的电商选品专家和市场分析师。请基于多平台搜索数据，进行全面的爆品选品分析。
            
            🎯 **分析要求：**
            1. **全平台对比**：深度分析抖音、淘宝、亚马逊、拼多多的市场表现
            2. **优缺点剖析**：详细分析每个平台产品的优势和劣势
            3. **需求洞察**：挖掘用户需求特点和消费心理
            4. **评分系统**：给出0-100分的专业评分
            
            严格按照以下JSON结构输出：
            
            {
              "category": "商品类目名称",
              "platforms": {
                "douyin": {
                  "market_trend": "详细市场趋势分析",
                  "hot_products": ["爆品1", "爆品2", "爆品3"],
                  "pros": ["优点1", "优点2", "优点3"],
                  "cons": ["缺点1", "缺点2", "缺点3"],
                  "demand_analysis": "用户需求特征和消费心理分析",
                  "opportunity_score": 85
                },
                "taobao": {
                  "market_trend": "详细市场趋势分析",
                  "hot_products": ["爆品1", "爆品2", "爆品3"],
                  "pros": ["优点1", "优点2", "优点3"],
                  "cons": ["缺点1", "缺点2", "缺点3"],
                  "demand_analysis": "用户需求特征和消费心理分析",
                  "opportunity_score": 78
                },
                "amazon": {
                  "market_trend": "详细市场趋势分析",
                  "hot_products": ["爆品1", "爆品2", "爆品3"],
                  "pros": ["优点1", "优点2", "优点3"],
                  "cons": ["缺点1", "缺点2", "缺点3"],
                  "demand_analysis": "用户需求特征和消费心理分析",
                  "opportunity_score": 82
                },
                "pinduoduo": {
                  "market_trend": "详细市场趋势分析",
                  "hot_products": ["爆品1", "爆品2", "爆品3"],
                  "pros": ["优点1", "优点2", "优点3"],
                  "cons": ["缺点1", "缺点2", "缺点3"],
                  "demand_analysis": "用户需求特征和消费心理分析",
                  "opportunity_score": 71
                }
              },
              "overall_analysis": {
                "market_size": "大",
                "competition_level": "中",
                "profit_potential": "高",
                "recommended_products": ["跨平台推荐产品1", "跨平台推荐产品2", "跨平台推荐产品3"],
                "market_gaps": ["市场空白机会1", "市场空白机会2"],
                "risk_factors": ["主要风险1", "主要风险2"],
                "unmet_needs": ["未满足需求1", "未满足需求2", "未满足需求3"]
              },
              "scores": {
                "overall_opportunity": 79,
                "market_potential": 85,
                "competition_risk": 45
              }
            }
        - role: user
          text: |
            请对【{{#keyword_extractor.structured_output.keyword#}}】进行全面的多平台爆品选品分析：

            🎯 **用户原始输入**: {{#start.user_input#}}
            🔍 **提取的关键词**: {{#keyword_extractor.structured_output.keyword#}}
            📂 **商品分类**: {{#keyword_extractor.structured_output.category#}}
            📊 **提取置信度**: {{#keyword_extractor.structured_output.confidence#}}
            
            🔍 **数据源：**
            📱 抖音数据: {{#douyin_search.body#}}
            🛒 淘宝数据: {{#taobao_search.body#}}
            🌍 亚马逊数据: {{#amazon_search.body#}}
            💰 拼多多数据: {{#pdd_search.body#}}

            🎯 **重点分析淘宝MCP数据**：
            淘宝数据来自MCP协议标准化爬虫，包含：
            - Model Context Protocol v1.12.2标准接口
            - 差评获取率提升到33.3%
            - 94.4%真实评价率
            - 结构化商品和评价数据
            - 智能标签分类系统
            
            请深度分析每个平台的优缺点、用户需求特征，并给出专业的选品建议。
        selected: false
        structured_output:
          schema:
            type: object
            properties:
              category:
                type: string
                description: 商品类目
              platforms:
                type: object
                properties:
                  douyin:
                    type: object
                    properties:
                      market_trend:
                        type: string
                        description: 抖音市场趋势
                      hot_products:
                        type: array
                        items:
                          type: string
                        description: 热门产品列表
                      pros:
                        type: array
                        items:
                          type: string
                        description: 平台优势
                      cons:
                        type: array
                        items:
                          type: string
                        description: 平台劣势
                      demand_analysis:
                        type: string
                        description: 需求分析
                      opportunity_score:
                        type: number
                        minimum: 0
                        maximum: 100
                        description: 机会评分
                    required: ["market_trend", "hot_products", "pros", "cons", "demand_analysis", "opportunity_score"]
                  taobao:
                    type: object
                    properties:
                      market_trend:
                        type: string
                        description: 淘宝市场趋势
                      hot_products:
                        type: array
                        items:
                          type: string
                        description: 热门产品列表
                      pros:
                        type: array
                        items:
                          type: string
                        description: 平台优势
                      cons:
                        type: array
                        items:
                          type: string
                        description: 平台劣势
                      demand_analysis:
                        type: string
                        description: 需求分析
                      opportunity_score:
                        type: number
                        minimum: 0
                        maximum: 100
                        description: 机会评分
                    required: ["market_trend", "hot_products", "pros", "cons", "demand_analysis", "opportunity_score"]
                  amazon:
                    type: object
                    properties:
                      market_trend:
                        type: string
                        description: 亚马逊市场趋势
                      hot_products:
                        type: array
                        items:
                          type: string
                        description: 热门产品列表
                      pros:
                        type: array
                        items:
                          type: string
                        description: 平台优势
                      cons:
                        type: array
                        items:
                          type: string
                        description: 平台劣势
                      demand_analysis:
                        type: string
                        description: 需求分析
                      opportunity_score:
                        type: number
                        minimum: 0
                        maximum: 100
                        description: 机会评分
                    required: ["market_trend", "hot_products", "pros", "cons", "demand_analysis", "opportunity_score"]
                  pinduoduo:
                    type: object
                    properties:
                      market_trend:
                        type: string
                        description: 拼多多市场趋势
                      hot_products:
                        type: array
                        items:
                          type: string
                        description: 热门产品列表
                      pros:
                        type: array
                        items:
                          type: string
                        description: 平台优势
                      cons:
                        type: array
                        items:
                          type: string
                        description: 平台劣势
                      demand_analysis:
                        type: string
                        description: 需求分析
                      opportunity_score:
                        type: number
                        minimum: 0
                        maximum: 100
                        description: 机会评分
                    required: ["market_trend", "hot_products", "pros", "cons", "demand_analysis", "opportunity_score"]
                required: ["douyin", "taobao", "amazon", "pinduoduo"]
              overall_analysis:
                type: object
                properties:
                  market_size:
                    type: string
                    enum: ["大", "中", "小"]
                    description: 市场规模
                  competition_level:
                    type: string
                    enum: ["高", "中", "低"]
                    description: 竞争程度
                  profit_potential:
                    type: string
                    enum: ["高", "中", "低"]
                    description: 盈利潜力
                  recommended_products:
                    type: array
                    items:
                      type: string
                    description: 推荐产品
                  market_gaps:
                    type: array
                    items:
                      type: string
                    description: 市场空白
                  risk_factors:
                    type: array
                    items:
                      type: string
                    description: 风险因素
                  unmet_needs:
                    type: array
                    items:
                      type: string
                    description: 未满足需求
                required: ["market_size", "competition_level", "profit_potential", "recommended_products", "market_gaps", "risk_factors", "unmet_needs"]
              scores:
                type: object
                properties:
                  overall_opportunity:
                    type: number
                    minimum: 0
                    maximum: 100
                    description: 总体机会评分
                  market_potential:
                    type: number
                    minimum: 0
                    maximum: 100
                    description: 市场潜力评分
                  competition_risk:
                    type: number
                    minimum: 0
                    maximum: 100
                    description: 竞争风险评分
                required: ["overall_opportunity", "market_potential", "competition_risk"]
            required: ["category", "platforms", "overall_analysis", "scores"]
        structured_output_enabled: true
        title: 智能分析引擎
        type: llm
        variables: []
        vision:
          enabled: false
      height: 96
      id: structured_analysis
      position:
        x: -100
        y: 350
      positionAbsolute:
        x: -100
        y: 350
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 智能评分计算器（保持步骤4.6的配置）
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: assign
          value:
          - structured_analysis
          - structured_output
          - platforms
          - douyin
          - opportunity_score
          variable_selector:
          - score_calculator
          - douyin_score
          write_mode: over-write
        - input_type: variable
          operation: assign
          value:
          - structured_analysis
          - structured_output
          - platforms
          - taobao
          - opportunity_score
          variable_selector:
          - score_calculator
          - taobao_score
          write_mode: over-write
        - input_type: variable
          operation: assign
          value:
          - structured_analysis
          - structured_output
          - platforms
          - amazon
          - opportunity_score
          variable_selector:
          - score_calculator
          - amazon_score
          write_mode: over-write
        - input_type: variable
          operation: assign
          value:
          - structured_analysis
          - structured_output
          - platforms
          - pinduoduo
          - opportunity_score
          variable_selector:
          - score_calculator
          - pdd_score
          write_mode: over-write
        - input_type: constant
          operation: assign
          value: "({{#score_calculator.douyin_score#}} + {{#score_calculator.taobao_score#}} + {{#score_calculator.amazon_score#}} + {{#score_calculator.pdd_score#}}) / 4"
          variable_selector:
          - score_calculator
          - average_platform_score
          write_mode: over-write
        - input_type: variable
          operation: assign
          value:
          - structured_analysis
          - structured_output
          - scores
          - overall_opportunity
          variable_selector:
          - score_calculator
          - overall_opportunity
          write_mode: over-write
        - input_type: variable
          operation: assign
          value:
          - structured_analysis
          - structured_output
          - scores
          - market_potential
          variable_selector:
          - score_calculator
          - market_potential
          write_mode: over-write
        - input_type: variable
          operation: assign
          value:
          - structured_analysis
          - structured_output
          - scores
          - competition_risk
          variable_selector:
          - score_calculator
          - competition_risk
          write_mode: over-write
        - input_type: constant
          operation: assign
          value: "{{#score_calculator.overall_opportunity#}} >= 80 ? '高' : ({{#score_calculator.overall_opportunity#}} >= 60 ? '中' : '低')"
          variable_selector:
          - score_calculator
          - recommendation_level
          write_mode: over-write
        - input_type: constant
          operation: assign
          value: "{{#score_calculator.competition_risk#}} >= 70 ? '需谨慎' : ({{#score_calculator.competition_risk#}} >= 40 ? '适中' : '风险较低')"
          variable_selector:
          - score_calculator
          - risk_level
          write_mode: over-write
        selected: false
        title: 智能评分计算器
        type: assigner
        version: '2'
      height: 172
      id: score_calculator
      position:
        x: 200
        y: 350
      positionAbsolute:
        x: 200
        y: 350
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 专业报告生成器
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.1
            max_tokens: 2000
          mode: chat
          name: gemini-2.5-pro
          provider: openai
        prompt_template:
        - role: system
          text: |
            你是一位顶级的电商选品顾问。请基于详细的分析数据和计算结果，生成专业、全面、易读的选品分析报告。
            
            🎯 **报告要求：**
            • 结构清晰，层次分明
            • 数据准确，洞察深刻
            • 建议实用，可操作性强
            • 包含优缺点对比和需求分析
            • 风险提示和投资建议明确
        - role: user
          text: |
            请基于以下数据生成专业的爆品选品分析报告：
            
            📊 **分析数据：**
            {{#structured_analysis.structured_output#}}
            
            🧮 **计算结果：**
            • 各平台评分：抖音{{#score_calculator.douyin_score#}}/淘宝{{#score_calculator.taobao_score#}}/亚马逊{{#score_calculator.amazon_score#}}/拼多多{{#score_calculator.pdd_score#}}
            • 平均评分：{{#score_calculator.average_platform_score#}}
            • 推荐等级：{{#score_calculator.recommendation_level#}}
            • 风险等级：{{#score_calculator.risk_level#}}
            
            请按以下结构生成报告：
            
            # 🛍️ {{category}} 爆品选品分析报告
            
            ## 📈 执行摘要
            [总体结论和核心建议]
            
            ## 🏆 平台对比分析
            ### 🏪 各平台评分排行
            | 平台 | 评分 | 排名 |
            |------|------|------|
            | 抖音 | {{douyin_score}}分 | - |
            | 淘宝 | {{taobao_score}}分 | - |
            | 亚马逊 | {{amazon_score}}分 | - |
            | 拼多多 | {{pdd_score}}分 | - |
            
            ## ✅ 优势分析
            [各平台优势对比]
            
            ## ❌ 劣势分析
            [各平台劣势对比]
            
            ## 🧠 需求洞察
            [用户需求特征分析]
            
            ## 💰 商业机会
            ### 🔥 推荐爆品
            [推荐产品列表]
            
            ### ⚡ 市场空白
            [市场空白机会]
            
            ### 🎯 未满足需求
            [未满足需求分析]
            
            ## ⚠️ 风险评估
            [风险因素和等级说明]
            
            ## 📊 评分详情
            ```
            综合评分系统：
            • 总体机会：{{overall_opportunity}}/100
            • 市场潜力：{{market_potential}}/100
            • 竞争风险：{{competition_risk}}/100
            • 平台平均：{{average_platform_score}}/100
            ```
            
            ## 🎯 投资建议
            ### 推荐等级：{{recommendation_level}}
            ### 风险等级：{{risk_level}}
            
            [具体行动建议]
            
            ## 📝 总结
            [3-5条关键要点]
        selected: false
        title: 专业报告生成器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 96
      id: format_output
      position:
        x: 500
        y: 350
      positionAbsolute:
        x: 500
        y: 350
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 最终答案节点
    - data:
        answer: |
          {{#format_output.text#}}
          
          ---
          
          ## 🔧 系统技术指标
          
          ### ✅ 功能验证
          - **多平台数据采集**: 4个平台并行搜索 ✅
          - **智能结构化分析**: JSON格式输出 ✅
          - **优缺点深度分析**: 全面对比评估 ✅
          - **需求洞察挖掘**: 消费心理分析 ✅
          - **智能评分系统**: 10项关键指标 ✅
          - **专业报告生成**: 可视化输出 ✅
          
          ### 📊 计算统计
          ```
          📱 抖音: {{#score_calculator.douyin_score#}}/100
          🛒 淘宝: {{#score_calculator.taobao_score#}}/100  
          🌍 亚马逊: {{#score_calculator.amazon_score#}}/100
          💰 拼多多: {{#score_calculator.pdd_score#}}/100
          
          📊 平均分: {{#score_calculator.average_platform_score#}}/100
          💡 推荐等级: {{#score_calculator.recommendation_level#}}
          ⚠️ 风险等级: {{#score_calculator.risk_level#}}
          ```
          
          ### 🎯 分析维度
          - **市场规模**: {{#structured_analysis.structured_output.overall_analysis.market_size#}}
          - **竞争程度**: {{#structured_analysis.structured_output.overall_analysis.competition_level#}}
          - **盈利潜力**: {{#structured_analysis.structured_output.overall_analysis.profit_potential#}}
          
          ---
          
          💡 **电商爆品选品专家** - 让数据驱动您的选品决策！
          
          🔄 **继续分析其他类目？请输入新的商品类目名称！**
        desc: ''
        selected: false
        title: 智能选品分析报告
        type: answer
        variables: []
      height: 280
      id: final_answer
      position:
        x: 800
        y: 300
      positionAbsolute:
        x: 800
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 300

    viewport:
      x: 700
      y: -150
      zoom: 0.6 