#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
淘宝爬虫MCP HTTP服务器
符合Dify MCP集成要求的HTTP传输版本
"""

import asyncio
import json
import logging
import sys
import os
from typing import Any, Dict, List, Optional

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# MCP相关导入
from fastmcp import FastMCP

# 导入最终版爬虫
try:
    from final_taobao_review_crawler import FinalTaobaoReviewCrawler
except ImportError:
    print("❌ 无法导入最终版爬虫，请确保文件存在")
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("taobao-mcp-http-server")

# 全局爬虫实例
crawler: Optional[FinalTaobaoReviewCrawler] = None

# 创建FastMCP服务器实例
mcp = FastMCP("淘宝爬虫MCP服务器")

@mcp.tool()
def crawl_taobao_products(
    keyword: str,
    max_products: int = 3,
    max_reviews: int = 10,
    include_reviews: bool = True,
    enable_debug: bool = False
) -> dict:
    """
    爬取淘宝商品数据，包括商品信息和用户评价
    
    Args:
        keyword: 搜索关键词，如'蓝牙耳机'
        max_products: 最大商品数量 (1-50)
        max_reviews: 每个商品的最大评价数量 (1-200)
        include_reviews: 是否包含详细评价
        enable_debug: 是否启用调试模式
    
    Returns:
        包含商品和评价数据的字典
    """
    global crawler
    
    try:
        logger.info(f"开始爬取: 关键词={keyword}, 商品数={max_products}, 评价数={max_reviews}")
        
        # 初始化爬虫
        if not crawler:
            crawler = FinalTaobaoReviewCrawler(headless=True, debug=enable_debug)
            logger.info("爬虫初始化成功")
        
        # 确保driver已创建并登录
        if not crawler.driver:
            crawler._create_driver()
            logger.info("WebDriver创建成功")
        
        if not crawler.is_logged_in:
            login_success = crawler.login_with_cookies()
            if login_success:
                logger.info("Cookies登录成功")
            else:
                logger.warning("Cookies登录失败，将尝试无登录模式")
        
        # 执行爬取
        result = crawler.get_products_with_reviews(
            keyword=keyword,
            max_results=max_products,
            max_reviews_per_product=max_reviews
        )
        
        if result and isinstance(result, list) and len(result) > 0:
            logger.info(f"爬取成功: {len(result)}个商品")
            
            # 统计评价数量
            total_reviews = sum(
                len(product.good_reviews) + len(product.bad_reviews) + len(product.medium_reviews)
                for product in result
            )
            
            # 转换为可序列化的格式
            products_data = []
            for product in result:
                product_dict = {
                    "title": product.title,
                    "price": product.price,
                    "sales": product.sales,
                    "shop": product.shop,
                    "location": product.location,
                    "detail_url": product.detail_url,
                    "good_reviews": [{"text": r.review_text, "type": r.review_type, "tags": r.tags} for r in product.good_reviews],
                    "bad_reviews": [{"text": r.review_text, "type": r.review_type, "tags": r.tags} for r in product.bad_reviews],
                    "medium_reviews": [{"text": r.review_text, "type": r.review_type, "tags": r.tags} for r in product.medium_reviews]
                }
                products_data.append(product_dict)
            
            # 返回结构化数据
            return {
                "success": True,
                "data": {
                    "products": products_data,
                    "summary": {
                        "total_products": len(result),
                        "total_reviews": total_reviews,
                        "keyword": keyword
                    }
                },
                "message": f"成功爬取{len(result)}个商品，{total_reviews}条评价",
                "crawler_info": {
                    "version": "v3.0 Final MCP HTTP",
                    "protocol": "Model Context Protocol HTTP",
                    "transport": "HTTP",
                    "features": [
                        "智能关键词提取",
                        "基于标签的评价分类",
                        "容器内精准滚动",
                        "真实评价验证过滤",
                        "差评获取突破",
                        "MCP HTTP标准化接口"
                    ]
                }
            }
        else:
            error_msg = '爬取失败或未获取到商品数据'
            logger.error(f"爬取失败: {error_msg}")
            
            return {
                "success": False,
                "error": error_msg,
                "message": "爬取失败，请检查网络连接和cookies状态"
            }
            
    except Exception as e:
        logger.error(f"爬取异常: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "爬取过程中发生异常"
        }

@mcp.tool()
def get_crawler_status() -> dict:
    """
    获取爬虫状态和版本信息
    
    Returns:
        包含服务状态信息的字典
    """
    global crawler
    
    return {
        "service": "淘宝爬虫MCP HTTP服务器",
        "version": "v3.0 Final MCP HTTP",
        "protocol": "Model Context Protocol",
        "transport": "HTTP",
        "status": "healthy",
        "crawler_ready": crawler is not None,
        "features": [
            "智能关键词提取",
            "基于标签的评价分类", 
            "容器内精准滚动",
            "真实评价验证过滤",
            "差评获取突破",
            "MCP HTTP标准化接口"
        ],
        "capabilities": {
            "max_products": 50,
            "max_reviews_per_product": 200,
            "supported_platforms": ["淘宝"],
            "output_formats": ["JSON", "结构化数据"],
            "dify_compatible": True
        },
        "endpoints": {
            "health": "/health",
            "tools": "/tools",
            "call_tool": "/call_tool"
        }
    }

@mcp.tool()
def health_check() -> dict:
    """
    健康检查端点
    
    Returns:
        服务健康状态
    """
    return {
        "status": "healthy",
        "service": "淘宝爬虫MCP HTTP服务器",
        "timestamp": asyncio.get_event_loop().time(),
        "version": "v3.0 Final MCP HTTP"
    }

def main():
    """主函数 - 启动HTTP服务器"""
    print("🚀 启动淘宝爬虫MCP HTTP服务器")
    print("=" * 50)
    print("📡 协议: Model Context Protocol (HTTP)")
    print("🌐 传输: HTTP (Dify兼容)")
    print("🔧 端口: 8001")
    print("🌐 绑定地址: *************")
    print("🌐 服务器URL: http://*************:8001/mcp/")
    print("📋 健康检查: http://*************:8001/health")
    print("🌍 局域网访问: 支持")
    print()
    
    try:
        # 初始化全局爬虫实例
        global crawler
        crawler = FinalTaobaoReviewCrawler(headless=True, debug=False)
        print("✅ 爬虫初始化成功")
        
        # 启动HTTP服务器
        mcp.run(transport="streamable-http", port=8001, host="*************")
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 清理资源
        if crawler:
            crawler.close()

if __name__ == "__main__":
    main()
