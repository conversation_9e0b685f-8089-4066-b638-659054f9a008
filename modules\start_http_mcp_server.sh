#!/bin/bash

echo "========================================"
echo "启动淘宝爬虫HTTP MCP服务器"
echo "========================================"
echo

# 激活conda环境
echo "激活conda环境: torch"
source activate torch
if [ $? -ne 0 ]; then
    echo "错误: 无法激活conda环境 torch"
    echo "请确保已安装conda并创建了torch环境"
    exit 1
fi

# 检查Python环境
echo "检查Python环境..."
python -c "import fastmcp; print('FastMCP库已安装')" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "错误: FastMCP库未安装"
    echo "请运行: pip install fastmcp"
    exit 1
fi

# 检查爬虫文件
if [ ! -f "final_taobao_review_crawler.py" ]; then
    echo "错误: 找不到爬虫文件 final_taobao_review_crawler.py"
    echo "请确保在正确的目录中运行此脚本"
    exit 1
fi

# 启动HTTP MCP服务器
echo
echo "启动HTTP MCP服务器..."
echo "服务器URL: http://*************:8001/mcp/"
echo "绑定地址: ************* (局域网地址)"
echo "按 Ctrl+C 停止服务器"
echo

python taobao_mcp_http_server.py

echo
echo "服务器已停止"
