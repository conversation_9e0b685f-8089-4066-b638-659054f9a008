#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YAML文件格式检查脚本
检查Dify工作流文件的语法是否正确
"""

import yaml
import sys

def check_yaml_file(filepath):
    """检查YAML文件格式"""
    print(f"🔍 检查YAML文件: {filepath}")
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试解析YAML
        data = yaml.safe_load(content)
        
        print("✅ YAML语法检查通过")
        
        # 检查基本结构
        if 'app' in data:
            print("✅ 包含app配置")
        else:
            print("❌ 缺少app配置")
        
        if 'workflow' in data:
            print("✅ 包含workflow配置")
            
            workflow = data['workflow']
            
            # 检查环境变量
            if 'environment_variables' in workflow:
                env_vars = workflow['environment_variables']
                print(f"✅ 包含 {len(env_vars)} 个环境变量")
                
                # 检查淘宝爬虫API变量
                taobao_var = None
                for var in env_vars:
                    if var.get('key') == 'TAOBAO_CRAWLER_API_URL':
                        taobao_var = var
                        break
                
                if taobao_var:
                    print("✅ 包含TAOBAO_CRAWLER_API_URL环境变量")
                else:
                    print("❌ 缺少TAOBAO_CRAWLER_API_URL环境变量")
            
            # 检查图结构
            if 'graph' in workflow:
                graph = workflow['graph']
                
                if 'nodes' in graph:
                    nodes = graph['nodes']
                    print(f"✅ 包含 {len(nodes)} 个节点")
                    
                    # 检查关键节点
                    node_titles = [node.get('data', {}).get('title', '') for node in nodes]
                    
                    if any('淘宝' in title for title in node_titles):
                        print("✅ 包含淘宝相关节点")
                    else:
                        print("❌ 缺少淘宝相关节点")
                    
                    if any('智能分析' in title for title in node_titles):
                        print("✅ 包含智能分析节点")
                    else:
                        print("❌ 缺少智能分析节点")
                
                if 'edges' in graph:
                    edges = graph['edges']
                    print(f"✅ 包含 {len(edges)} 个连接")
        
        else:
            print("❌ 缺少workflow配置")
        
        print(f"\n📊 文件统计:")
        print(f"   文件大小: {len(content)} 字符")
        print(f"   行数: {content.count(chr(10)) + 1}")
        
        return True
        
    except yaml.YAMLError as e:
        print(f"❌ YAML语法错误:")
        print(f"   错误信息: {e}")
        
        # 尝试找到错误位置
        if hasattr(e, 'problem_mark'):
            mark = e.problem_mark
            print(f"   错误位置: 第{mark.line + 1}行, 第{mark.column + 1}列")
        
        return False
        
    except Exception as e:
        print(f"❌ 文件读取错误: {e}")
        return False

def main():
    """主函数"""
    print("🔍 Dify工作流YAML文件检查工具")
    print("=" * 50)
    
    # 检查目标文件
    filepath = "步骤4.11-最终集成版.yml"
    
    if check_yaml_file(filepath):
        print("\n🎉 文件格式检查通过！")
        print("✅ 可以导入到Dify中使用")
    else:
        print("\n❌ 文件格式有问题！")
        print("请修复错误后重新检查")

if __name__ == "__main__":
    main()
