#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试MCP服务器连接性
用于验证服务器是否可以被Dify正常访问
"""

import requests
import json
import time

def test_mcp_server():
    """测试MCP服务器连接性"""
    print("🔍 快速测试MCP服务器连接性")
    print("=" * 50)
    
    server_url = "http://127.0.0.1:8001/mcp/"
    print(f"📡 测试URL: {server_url}")
    
    # 测试1: 基本连接测试
    print("\n1️⃣ 基本连接测试...")
    try:
        response = requests.get(server_url, timeout=5)
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 406:
            print("   ✅ 服务器正常运行（406是预期的MCP响应）")
        elif response.status_code == 503:
            print("   ❌ 503错误 - 服务器不可用")
            return False
        else:
            print(f"   ⚠️  意外状态码: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("   ❌ 连接失败 - 服务器可能未启动")
        return False
    except requests.exceptions.Timeout:
        print("   ❌ 连接超时")
        return False
    except Exception as e:
        print(f"   ❌ 连接错误: {e}")
        return False
    
    # 测试2: MCP协议头测试
    print("\n2️⃣ MCP协议头测试...")
    try:
        headers = {
            'Accept': 'text/event-stream',
            'Content-Type': 'application/json'
        }
        response = requests.get(server_url, headers=headers, timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 400:
            print("   ✅ MCP协议响应正常（400是预期的，缺少session ID）")
        else:
            print(f"   响应内容: {response.text[:200]}...")
            
    except Exception as e:
        print(f"   ❌ MCP协议测试失败: {e}")
        return False
    
    # 测试3: 端口可达性测试
    print("\n3️⃣ 端口可达性测试...")
    import socket
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(('127.0.0.1', 8001))
        sock.close()
        
        if result == 0:
            print("   ✅ 端口8001可达")
        else:
            print("   ❌ 端口8001不可达")
            return False
            
    except Exception as e:
        print(f"   ❌ 端口测试失败: {e}")
        return False
    
    # 测试4: 代理检查
    print("\n4️⃣ 代理环境检查...")
    import os
    proxy_vars = ['http_proxy', 'HTTP_PROXY', 'https_proxy', 'HTTPS_PROXY']
    proxy_found = False
    
    for var in proxy_vars:
        if os.environ.get(var):
            print(f"   ⚠️  发现代理设置: {var}={os.environ.get(var)}")
            proxy_found = True
    
    if not proxy_found:
        print("   ✅ 未发现代理设置")
    else:
        print("   💡 建议：如果连接失败，尝试临时禁用代理")
    
    print("\n🎉 连接性测试完成！")
    print("\n📋 Dify配置信息:")
    print(f"   Server URL: {server_url}")
    print("   Server Name: 淘宝爬虫MCP服务器")
    print("   Server ID: taobao-crawler-http")
    
    return True

def check_server_process():
    """检查服务器进程是否运行"""
    print("\n🔍 检查服务器进程...")
    
    import psutil
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if 'taobao_mcp_http_server.py' in cmdline:
                        print(f"   ✅ 找到MCP服务器进程: PID {proc.info['pid']}")
                        return True
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        print("   ❌ 未找到MCP服务器进程")
        print("   💡 请运行: python taobao_mcp_http_server.py")
        return False
        
    except ImportError:
        print("   ⚠️  psutil未安装，跳过进程检查")
        return True

def main():
    """主函数"""
    print("🚀 MCP服务器快速诊断工具")
    print("用于解决Dify连接503错误")
    print()
    
    # 检查进程
    process_ok = check_server_process()
    
    # 测试连接
    connection_ok = test_mcp_server()
    
    print("\n" + "="*50)
    if process_ok and connection_ok:
        print("✅ 诊断结果: MCP服务器运行正常")
        print("💡 现在可以在Dify中添加MCP服务器了")
        print("   URL: http://127.0.0.1:8001/mcp/")
    else:
        print("❌ 诊断结果: 发现问题")
        print("💡 请按照故障排除指南解决问题")
        print("   参考: TROUBLESHOOTING_503_ERROR.md")

if __name__ == "__main__":
    main()
