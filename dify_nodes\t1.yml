app:
  description: 智能电商爆品选品系统：自动抓取抖音/淘宝/亚马逊/拼多多热门商品数据，使用AI分析产品优缺点、市场需求和竞争机会，生成详细的选品报告
  icon: 🛍️
  icon_background: '#FF6B35'
  mode: advanced-chat
  name: 电商爆品选品智能分析系统
  use_icon_as_answer_icon: false

dependencies: []
kind: app
version: 0.3.0

workflow:
  features:
    file_upload:
      allowed_file_extensions:
        - .JPG
        - .JPEG
        - .PNG
        - .GIF
        - .WEBP
        - .SVG
        - .PDF
        - .TXT
        - .DOCX
        - .XLSX
      allowed_file_types:
        - image
        - document
      allowed_file_upload_methods:
        - local_file
        - remote_url
      enabled: true
      image:
        enabled: true
        number_limits: 5
        transfer_methods:
          - local_file
          - remote_url
      number_limits: 10
    opening_statement: |
      👋 欢迎使用电商爆品选品智能分析系统！
      
      我可以帮您：
      🔍 分析特定类目的热门商品
      📊 提供详细的优缺点分析
      💡 识别市场机会和用户需求
      📈 生成专业的选品报告
      
      请告诉我您想分析的商品类目（如：家居服、数码配件、美妆护肤等），我将为您提供全面的市场分析！
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
      - "分析一下家居服类目的爆品趋势"
      - "帮我找找数码配件的市场机会"
      - "美妆护肤品有什么值得关注的产品"
      - "运动健身类目的热门商品分析"
    suggested_questions_after_answer:
      enabled: true
    text_to_speech:
      enabled: false

  environment_variables:
    - key: MCP_ENDPOINT
      value: http://localhost:8000
      description: MCP服务器统一接入地址
    - key: ANALYSIS_DEPTH
      value: detailed
      description: 分析深度配置：basic/detailed/comprehensive

  conversation_variables:
    - id: category
      name: 商品类目
      description: 用户指定的商品分析类目
      value_type: string
    - id: platform_preference
      name: 平台偏好
      description: 用户偏好的电商平台
      value_type: string

  graph:
    nodes:
      # ========== 1. 工作流启动节点 ==========
      - id: start
        type: start
        data:
          title: 🚀 启动分析
          type: start
          variables:
            - variable: category
              type: string
              label: 商品类目
              max_length: 50
              options: []
              description: 请输入要分析的商品类目
              required: true