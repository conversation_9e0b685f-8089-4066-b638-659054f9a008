#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试代理绕过设置是否生效
"""

import requests
import os
import time

def test_proxy_bypass():
    """测试代理绕过设置"""
    print("🔍 测试代理绕过设置")
    print("=" * 40)
    
    # 显示当前代理设置
    print("📋 当前代理环境变量:")
    proxy_vars = ['http_proxy', 'HTTP_PROXY', 'https_proxy', 'HTTPS_PROXY', 'no_proxy', 'NO_PROXY']
    for var in proxy_vars:
        value = os.environ.get(var, '未设置')
        print(f"   {var}: {value}")
    
    print("\n🌐 测试连接...")
    
    # 测试URL列表
    test_urls = [
        "http://127.0.0.1:8001/mcp/",
        "http://localhost:8001/mcp/"
    ]
    
    for url in test_urls:
        print(f"\n📡 测试: {url}")
        try:
            # 不使用代理的请求
            response = requests.get(url, timeout=5, proxies={'http': None, 'https': None})
            print(f"   ✅ 直连成功: {response.status_code}")
            
            # 使用系统代理的请求
            response_with_proxy = requests.get(url, timeout=5)
            print(f"   📊 系统代理: {response_with_proxy.status_code}")
            
            if response.status_code == response_with_proxy.status_code:
                print(f"   🎉 代理绕过生效！")
            else:
                print(f"   ⚠️  代理绕过可能未生效")
                
        except requests.exceptions.ConnectionError as e:
            print(f"   ❌ 连接失败: {e}")
        except requests.exceptions.Timeout:
            print(f"   ⏰ 连接超时")
        except Exception as e:
            print(f"   ❌ 其他错误: {e}")

def show_proxy_config_guide():
    """显示代理配置指南"""
    print("\n📚 代理配置指南")
    print("=" * 40)
    
    print("🔹 Clash for Windows:")
    print("   Settings → System Proxy → Bypass Domain/IPNet")
    print("   添加: 127.0.0.1,localhost,127.0.0.1:8001")
    
    print("\n🔹 V2rayN:")
    print("   设置 → Core：基础设置 → 本地策略 → 绕过的域名")
    print("   添加: localhost,127.0.0.1")
    
    print("\n🔹 环境变量方式:")
    print("   运行: set_proxy_bypass.bat")
    print("   或在PowerShell中运行: .\\set_proxy_bypass.ps1")
    
    print("\n🔹 临时禁用代理:")
    print("   unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY")

if __name__ == "__main__":
    test_proxy_bypass()
    show_proxy_config_guide()
