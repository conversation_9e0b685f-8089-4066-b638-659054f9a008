app:
  description: 每日自动抓取抖音/淘宝/亚马逊/拼多多家居服爆品 → LLM 提炼 → 评分 → 写飞书
  icon: 🏠
  icon_background: '#E0F2FE'
  mode: advanced-chat            # ✅ Dify requirement
  name: 家居服爆品选品工作流
  use_icon_as_answer_icon: false
dependencies: []                # ✅ match sample
kind: app
version: 0.3.0

workflow:
  features:
    file_upload:
      allowed_file_extensions:
        - .JPG
        - .JPEG
        - .PNG
        - .GIF
        - .WEBP
        - .SVG
      allowed_file_types:
        - image
      allowed_file_upload_methods:
        - local_file
        - remote_url
      enabled: true          # ✅ align with sample
      image:
        enabled: true
        number_limits: 3
        transfer_methods:
          - local_file
          - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false

  environment_variables:
    - key: MCP_ENDPOINT
      value: http://localhost:8000
      description: MCP Server 统一地址
    # 如需更多环境变量可追加

  conversation_variables: []

  graph:
    nodes:
      # ---------- 1. START ----------
      - id: start
        type: start
        data:
          title: 开始
          type: start
          variables: []

      # ---------- 2. 并行爬虫 ----------
      - id: crawl_douyin
        type: tool
        data:
          title: 抖音 Top20
          tool_name: douyin_top20
          tool_provider: mcp
          parameters:
            keyword: 家居服
        upstream: [start]

      - id: crawl_taobao
        type: tool
        data:
          title: 淘宝 Top20
          tool_name: taobao_top20
          tool_provider: mcp
          parameters:
            keyword: 家居服
        upstream: [start]

      - id: crawl_amazon
        type: tool
        data:
          title: Amazon Top20
          tool_name: amazon_top20
          tool_provider: mcp
          parameters:
            keyword: home clothes
        upstream: [start]

      - id: crawl_pdd
        type: tool
        data:
          title: 拼多多 Top20
          tool_name: pdd_top20
          tool_provider: mcp
          parameters:
            keyword: 家居服
        upstream: [start]

      # ---------- 3. LLM 提炼 ----------
      - id: llm_extract
        type: llm
        data:
          title: LLM 提炼
          model:
            provider: langgenius/openai/openai
            name: gpt-4o-mini
            mode: chat
            completion_params:
              temperature: 0.3
              max_tokens: 512
          prompt_template:
            - role: system
              text: |
                你是资深电商选品专家。  
                输入为某平台爆品的标题、售价、卖点、Top5 评论，输出 JSON：
                {
                  "pros": ["最多3条核心卖点"],
                  "cons": ["最多3条用户痛点"],
                  "unmet_needs": ["最多2条未被满足的需求"],
                  "diff_suggestion": "一句话差异化建议"
                }
            - role: user
              text: |
                标题: {{title}}
                售价: {{price}}
                卖点: {{selling_point}}
                评论: {{reviews}}
        upstream: [crawl_douyin, crawl_taobao, crawl_amazon, crawl_pdd]

      # ---------- 4. 机会评分 ----------
      - id: score
        type: tool
        data:
          title: 机会评分
          tool_name: opportunity_score
          tool_provider: mcp
          parameters:
            item: "{{ llm_extract.result }}"
            platform: "{{ llm_extract.metadata.platform }}"
        upstream: [llm_extract]

      # ---------- 5. 写飞书 ----------
      - id: write_lark
        type: tool
        data:
          title: 写入飞书
          tool_name: append_lark
          tool_provider: mcp
          parameters:
            sheet: 家居服选品
            rows:
              - date: "{{ now() | date('Y-m-d') }}"
                platform: "{{ llm_extract.metadata.platform }}"
                title: "{{ llm_extract.result.title }}"
                price: "{{ llm_extract.result.price }}"
                margin: "{{ score.result.margin }}"
                score: "{{ score.result.score }}"
                cons: "{{ llm_extract.result.cons | join('; ') }}"
                diff_suggestion: "{{ llm_extract.result.diff_suggestion }}"
        upstream: [score]

      # ---------- 6. 最终答复 ----------
      - id: answer
        type: answer
        data:
          title: 直接回复
          answer: "✅ 今日家居服选品已更新到飞书表格！"
        upstream: [write_lark]

    edges:
      # Edges are implicitly defined via upstream arrays