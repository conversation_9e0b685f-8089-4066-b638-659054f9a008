#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试局域网MCP服务器连接性
验证*************绑定是否正常工作
"""

import requests
import socket
import time

def test_lan_mcp_server():
    """测试局域网MCP服务器"""
    print("🌐 测试局域网MCP服务器连接性")
    print("=" * 50)
    
    server_ip = "*************"
    server_port = 8001
    server_url = f"http://{server_ip}:{server_port}/mcp/"
    
    print(f"📡 测试服务器: {server_url}")
    print(f"🌍 绑定地址: {server_ip}")
    print(f"🔧 端口: {server_port}")
    
    # 测试1: 端口连通性
    print(f"\n1️⃣ 端口连通性测试...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((server_ip, server_port))
        sock.close()
        
        if result == 0:
            print(f"   ✅ 端口 {server_port} 可达")
        else:
            print(f"   ❌ 端口 {server_port} 不可达")
            return False
            
    except Exception as e:
        print(f"   ❌ 端口测试失败: {e}")
        return False
    
    # 测试2: HTTP连接测试
    print(f"\n2️⃣ HTTP连接测试...")
    try:
        response = requests.get(server_url, timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: Content-Type = {response.headers.get('Content-Type', 'N/A')}")
        
        if response.status_code == 406:
            print("   ✅ MCP服务器正常响应（406是预期的）")
        elif response.status_code == 503:
            print("   ❌ 503错误 - 服务器不可用")
            return False
        else:
            print(f"   ⚠️  状态码: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("   ❌ 连接失败 - 检查服务器是否运行")
        return False
    except requests.exceptions.Timeout:
        print("   ❌ 连接超时")
        return False
    except Exception as e:
        print(f"   ❌ HTTP测试失败: {e}")
        return False
    
    # 测试3: MCP协议测试
    print(f"\n3️⃣ MCP协议测试...")
    try:
        headers = {
            'Accept': 'text/event-stream',
            'Content-Type': 'application/json'
        }
        response = requests.get(server_url, headers=headers, timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 400:
            print("   ✅ MCP协议响应正常（400 - 缺少session ID）")
        else:
            print(f"   响应内容: {response.text[:100]}...")
            
    except Exception as e:
        print(f"   ❌ MCP协议测试失败: {e}")
        return False
    
    # 测试4: 网络接口检查
    print(f"\n4️⃣ 网络接口检查...")
    try:
        import subprocess
        result = subprocess.run(['ipconfig'], capture_output=True, text=True, timeout=10)
        if server_ip in result.stdout:
            print(f"   ✅ 找到网络接口 {server_ip}")
        else:
            print(f"   ⚠️  未在ipconfig中找到 {server_ip}")
            print("   💡 请确认IP地址是否正确")
    except Exception as e:
        print(f"   ⚠️  网络接口检查失败: {e}")
    
    print(f"\n🎉 局域网MCP服务器测试完成！")
    return True

def show_dify_config():
    """显示Dify配置信息"""
    print(f"\n📋 Dify MCP服务器配置")
    print("=" * 40)
    print(f"🌐 Server URL: http://*************:8001/mcp/")
    print(f"📝 Server Name: 淘宝爬虫MCP服务器")
    print(f"🆔 Server ID: taobao-crawler-http")
    print()
    print("✨ 优势:")
    print("   ✅ 避免localhost代理问题")
    print("   ✅ 支持局域网内其他设备访问")
    print("   ✅ 更稳定的网络连接")
    print("   ✅ 防火墙友好")

def test_from_different_addresses():
    """从不同地址测试连接"""
    print(f"\n🔄 多地址连接测试")
    print("-" * 30)
    
    test_addresses = [
        "http://*************:8001/mcp/",
        "http://localhost:8001/mcp/",
        "http://127.0.0.1:8001/mcp/"
    ]
    
    for addr in test_addresses:
        print(f"📡 测试: {addr}")
        try:
            response = requests.get(addr, timeout=5)
            if response.status_code in [400, 406]:
                print(f"   ✅ 可访问 ({response.status_code})")
            else:
                print(f"   ⚠️  状态码: {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"   ❌ 连接失败")
        except Exception as e:
            print(f"   ❌ 错误: {e}")

if __name__ == "__main__":
    success = test_lan_mcp_server()
    
    if success:
        show_dify_config()
        test_from_different_addresses()
        
        print(f"\n" + "="*50)
        print("✅ 诊断结果: 局域网MCP服务器运行正常")
        print("💡 现在可以在Dify中使用以下URL:")
        print("   http://*************:8001/mcp/")
        print()
        print("🎯 推荐使用*************地址，避免代理问题")
    else:
        print(f"\n" + "="*50)
        print("❌ 诊断结果: 发现问题")
        print("💡 请检查:")
        print("   1. 服务器是否正在运行")
        print("   2. IP地址*************是否正确")
        print("   3. 防火墙是否阻止了8001端口")
