#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HTTP MCP客户端
验证淘宝爬虫HTTP MCP服务器功能
"""

import asyncio
import json
import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mcp import ClientSession
from mcp.client.streamable_http import streamablehttp_client

async def test_http_mcp_server():
    """测试HTTP MCP服务器"""
    print("🌐 测试HTTP MCP服务器")
    print("=" * 50)
    
    server_url = "http://localhost:8000/mcp/"
    print(f"📡 连接到: {server_url}")
    
    try:
        # 创建HTTP客户端连接
        async with streamablehttp_client(server_url) as (read_stream, write_stream, get_session_id):
            async with ClientSession(read_stream, write_stream) as session:
                print("✅ HTTP MCP客户端连接成功")
                
                # 初始化连接
                print("\n🔧 初始化MCP连接...")
                await session.initialize()
                print("✅ MCP连接初始化成功")
                
                # 列出可用工具
                print("\n🛠️  列出可用工具...")
                tools_response = await session.list_tools()
                print(f"发现 {len(tools_response.tools)} 个工具:")
                for tool in tools_response.tools:
                    print(f"  - {tool.name}: {tool.description}")
                
                # 测试状态工具
                print("\n📊 测试状态工具...")
                try:
                    status_result = await session.call_tool("get_crawler_status", arguments={})
                    print("✅ 状态工具调用成功")
                    if status_result.content:
                        content = status_result.content[0]
                        if hasattr(content, 'text'):
                            try:
                                status_data = json.loads(content.text)
                                print(f"   服务名称: {status_data.get('service', 'N/A')}")
                                print(f"   服务版本: {status_data.get('version', 'N/A')}")
                                print(f"   传输方式: {status_data.get('transport', 'N/A')}")
                                print(f"   Dify兼容: {status_data.get('capabilities', {}).get('dify_compatible', False)}")
                            except json.JSONDecodeError:
                                print("   状态数据解析失败")
                except Exception as e:
                    print(f"⚠️  状态工具调用失败: {e}")
                
                # 测试爬虫工具
                print("\n🕷️  测试爬虫工具...")
                test_args = {
                    'keyword': '无线鼠标',
                    'max_products': 1,
                    'max_reviews': 3,
                    'include_reviews': True,
                    'enable_debug': False
                }
                
                print(f"   搜索参数: {test_args}")
                
                try:
                    crawl_result = await session.call_tool("crawl_taobao_products", arguments=test_args)
                    print("✅ 爬虫工具调用成功")
                    
                    if crawl_result.content:
                        content = crawl_result.content[0]
                        if hasattr(content, 'text'):
                            try:
                                data = json.loads(content.text)
                                if data.get('success'):
                                    summary = data.get('data', {}).get('summary', {})
                                    print(f"   📦 商品数量: {summary.get('total_products', 0)}")
                                    print(f"   💬 评价数量: {summary.get('total_reviews', 0)}")
                                    print(f"   🔧 爬虫版本: {data.get('crawler_info', {}).get('version', 'N/A')}")
                                    
                                    # 显示第一个商品信息
                                    products = data.get('data', {}).get('products', [])
                                    if products:
                                        product = products[0]
                                        print(f"   🏷️  商品标题: {product.get('title', 'N/A')[:50]}...")
                                        print(f"   💰 商品价格: {product.get('price', 'N/A')}")
                                        
                                        # 评价统计
                                        good_count = len(product.get('good_reviews', []))
                                        bad_count = len(product.get('bad_reviews', []))
                                        medium_count = len(product.get('medium_reviews', []))
                                        print(f"   📊 评价分布: 好评{good_count}条, 差评{bad_count}条, 中评{medium_count}条")
                                else:
                                    print(f"   ❌ 爬取失败: {data.get('error', '未知错误')}")
                            except json.JSONDecodeError:
                                print("   爬取数据解析失败")
                                print(f"   原始内容: {content.text[:200]}...")
                except Exception as e:
                    print(f"⚠️  爬虫工具调用失败: {e}")
                
                print("\n🎉 HTTP MCP服务器测试完成!")
                print("✅ 服务器符合Dify MCP集成要求")
                
    except Exception as e:
        print(f"❌ HTTP MCP测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_dify_integration_simulation():
    """模拟Dify集成测试"""
    print("\n🔗 模拟Dify集成测试")
    print("=" * 40)
    
    print("📋 Dify MCP集成步骤:")
    print("1. 在Dify中添加MCP服务器")
    print("   - 服务器URL: http://localhost:8000/mcp/")
    print("   - 服务器名称: 淘宝爬虫MCP服务器")
    print("   - 服务器ID: taobao-crawler-http")
    print()
    print("2. 授权和工具发现")
    print("   - Dify将自动发现可用工具")
    print("   - 无需额外授权 (公开服务)")
    print()
    print("3. 在应用中使用工具")
    print("   - Agent应用: 直接选择MCP工具")
    print("   - 工作流应用: 添加MCP工具节点")
    print()
    print("4. 工具参数配置")
    print("   - keyword: Auto (让AI决定)")
    print("   - max_products: Fixed Value (如: 3)")
    print("   - max_reviews: Fixed Value (如: 10)")
    print("   - include_reviews: Fixed Value (true)")
    print("   - enable_debug: Fixed Value (false)")
    print()
    print("✅ 现在可以在Dify中使用这个MCP服务器了!")

if __name__ == "__main__":
    async def main():
        await test_http_mcp_server()
        await test_dify_integration_simulation()
    
    asyncio.run(main())
