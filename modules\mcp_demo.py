#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP协议功能演示
展示完整的Model Context Protocol功能
"""

import asyncio
import json
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def demonstrate_mcp_features():
    """演示MCP协议的各种功能"""
    print("🎯 MCP协议功能演示")
    print("=" * 60)
    print("Model Context Protocol (MCP) 是一个标准化协议")
    print("用于LLM应用程序与外部数据源和工具的交互")
    print()
    
    # 服务器参数
    server_params = StdioServerParameters(
        command="python",
        args=["taobao_mcp_server.py"],
        env=os.environ.copy()
    )
    
    try:
        print("📡 建立MCP连接...")
        async with stdio_client(server_params) as (read_stream, write_stream):
            async with ClientSession(read_stream, write_stream) as session:
                print("✅ MCP客户端连接成功")
                
                # 1. 初始化协议
                print("\n🔧 1. 协议初始化")
                print("-" * 30)
                await session.initialize()
                print("✅ MCP协议握手完成")
                print("📋 协议版本: 2024-11-05")
                print("🔗 传输方式: stdio")
                
                # 2. 服务器能力发现
                print("\n🛠️  2. 服务器能力发现")
                print("-" * 30)
                tools_response = await session.list_tools()
                print(f"🔧 发现工具数量: {len(tools_response.tools)}")
                
                for i, tool in enumerate(tools_response.tools, 1):
                    print(f"   {i}. {tool.name}")
                    print(f"      描述: {tool.description}")
                    if hasattr(tool, 'inputSchema') and tool.inputSchema:
                        required = tool.inputSchema.get('required', [])
                        properties = tool.inputSchema.get('properties', {})
                        print(f"      参数: {len(properties)}个 (必需: {len(required)}个)")
                
                # 3. 工具调用演示
                print("\n🔍 3. 工具调用演示")
                print("-" * 30)
                
                # 调用状态工具
                print("📊 调用状态工具...")
                status_result = await session.call_tool("get_crawler_status", arguments={})
                if status_result.content:
                    content = status_result.content[0]
                    if hasattr(content, 'text'):
                        try:
                            status_data = json.loads(content.text)
                            print(f"   服务名称: {status_data.get('service', 'N/A')}")
                            print(f"   服务版本: {status_data.get('version', 'N/A')}")
                            print(f"   服务状态: {status_data.get('status', 'N/A')}")
                            print(f"   支持功能: {len(status_data.get('features', []))}项")
                        except json.JSONDecodeError:
                            print("   状态数据格式错误")
                
                # 调用爬虫工具
                print("\n🕷️  调用爬虫工具...")
                crawl_args = {
                    'keyword': '机械键盘',
                    'max_products': 1,
                    'max_reviews': 2,
                    'include_reviews': True,
                    'enable_debug': False
                }
                print(f"   搜索关键词: {crawl_args['keyword']}")
                print(f"   最大商品数: {crawl_args['max_products']}")
                print(f"   最大评价数: {crawl_args['max_reviews']}")
                
                crawl_result = await session.call_tool("crawl_taobao_products", arguments=crawl_args)
                
                if crawl_result.content:
                    content = crawl_result.content[0]
                    if hasattr(content, 'text'):
                        try:
                            crawl_data = json.loads(content.text)
                            if crawl_data.get('success'):
                                summary = crawl_data.get('data', {}).get('summary', {})
                                print(f"   ✅ 爬取成功!")
                                print(f"   📦 商品数量: {summary.get('total_products', 0)}")
                                print(f"   💬 评价数量: {summary.get('total_reviews', 0)}")
                                
                                # 显示商品信息
                                products = crawl_data.get('data', {}).get('products', [])
                                if products:
                                    product = products[0]
                                    print(f"   🏷️  商品标题: {product.get('title', 'N/A')[:50]}...")
                                    print(f"   💰 商品价格: {product.get('price', 'N/A').replace(chr(10), ' ')}")
                                    
                                    # 显示评价统计
                                    good_count = len(product.get('good_reviews', []))
                                    bad_count = len(product.get('bad_reviews', []))
                                    medium_count = len(product.get('medium_reviews', []))
                                    print(f"   📊 评价分布: 好评{good_count}条, 差评{bad_count}条, 中评{medium_count}条")
                            else:
                                print(f"   ❌ 爬取失败: {crawl_data.get('error', '未知错误')}")
                        except json.JSONDecodeError:
                            print("   爬取数据格式错误")
                
                # 4. 协议特性总结
                print("\n🎉 4. MCP协议特性总结")
                print("-" * 30)
                print("✅ 标准化通信: 使用JSON-RPC 2.0协议")
                print("✅ 工具发现: 动态发现服务器提供的工具")
                print("✅ 类型安全: 工具参数和返回值有明确的JSON Schema")
                print("✅ 错误处理: 标准化的错误响应格式")
                print("✅ 异步支持: 完全异步的通信模式")
                print("✅ 传输无关: 支持stdio、HTTP、WebSocket等传输方式")
                
                print("\n🚀 MCP协议演示完成!")
                print("💡 这个服务器现在可以被任何支持MCP的LLM客户端使用")
                print("   例如: Claude Desktop, 自定义AI应用等")
                
    except Exception as e:
        print(f"❌ MCP演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(demonstrate_mcp_features())
