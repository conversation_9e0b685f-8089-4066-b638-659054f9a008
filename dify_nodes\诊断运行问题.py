#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断Dify工作流运行问题
检查API服务器状态和数据流
"""

import requests
import json
import time

def check_api_server():
    """检查API服务器状态"""
    print("🔍 检查API服务器状态...")
    
    try:
        # 检查健康状态
        response = requests.get("http://localhost:8000/api/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API服务器运行正常")
            print(f"   服务: {data.get('service')}")
            print(f"   版本: {data.get('version')}")
            print(f"   状态: {data.get('status')}")
            return True
        else:
            print(f"❌ API响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到API服务器")
        print("请确保已启动API服务器: python start_api_server.py")
        return False
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

def test_category_processing():
    """测试类目处理"""
    print("\n🧪 测试类目处理...")
    
    test_categories = [
        "蓝牙耳机",
        "手机壳", 
        "充电器",
        "数码配件",
        "运动健身"
    ]
    
    for category in test_categories:
        print(f"\n📝 测试类目: {category}")
        
        test_data = {
            "keyword": category,
            "max_products": 3,
            "max_reviews": 20,
            "include_reviews": True,
            "enable_debug": True
        }
        
        try:
            response = requests.post(
                "http://localhost:8000/api/crawl",
                json=test_data,
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"   ✅ 类目识别成功")
                    print(f"   商品数: {data['data']['summary']['total_products']}")
                    print(f"   评价数: {data['data']['summary']['total_reviews']}")
                else:
                    print(f"   ❌ 爬取失败: {data.get('error')}")
            else:
                print(f"   ❌ API请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")
        
        time.sleep(2)  # 避免请求过快

def check_dify_variables():
    """检查Dify变量配置"""
    print("\n🔧 Dify变量配置检查...")
    
    print("📋 需要配置的环境变量:")
    print("   变量名: TAOBAO_CRAWLER_API_URL")
    print("   变量值: http://localhost:8000/api/crawl")
    print("   描述: 🎯 最终版淘宝爬虫API地址")
    
    print("\n📝 变量引用格式:")
    print("   在工作流中: {{#env.TAOBAO_CRAWLER_API_URL#}}")
    print("   在请求体中: {{#start.category#}}")

def check_common_issues():
    """检查常见问题"""
    print("\n🔍 常见问题检查...")
    
    issues = [
        {
            "问题": "无法识别类目",
            "可能原因": [
                "API服务器未启动",
                "环境变量配置错误", 
                "网络连接问题",
                "类目名称格式问题"
            ],
            "解决方案": [
                "启动API服务器: python start_api_server.py",
                "检查环境变量: TAOBAO_CRAWLER_API_URL",
                "测试网络连接: curl http://localhost:8000/api/health",
                "使用简单类目名称，如'蓝牙耳机'"
            ]
        },
        {
            "问题": "工作流执行失败",
            "可能原因": [
                "节点连接错误",
                "数据格式不匹配",
                "超时设置过短",
                "权限问题"
            ],
            "解决方案": [
                "检查节点连接关系",
                "验证数据格式",
                "增加超时时间",
                "检查API权限"
            ]
        }
    ]
    
    for i, issue in enumerate(issues, 1):
        print(f"\n{i}. {issue['问题']}")
        print("   可能原因:")
        for reason in issue['可能原因']:
            print(f"     - {reason}")
        print("   解决方案:")
        for solution in issue['解决方案']:
            print(f"     ✅ {solution}")

def provide_debug_steps():
    """提供调试步骤"""
    print("\n🛠️ 调试步骤...")
    
    steps = [
        "1. 检查API服务器状态",
        "2. 验证环境变量配置", 
        "3. 测试简单类目",
        "4. 查看Dify执行日志",
        "5. 检查网络连接",
        "6. 验证数据格式"
    ]
    
    for step in steps:
        print(f"   {step}")
    
    print("\n📞 如果问题持续存在:")
    print("   1. 查看Dify工作流执行日志")
    print("   2. 检查具体的错误信息")
    print("   3. 尝试使用模拟数据测试")
    print("   4. 逐个节点调试")

def main():
    """主函数"""
    print("🔍 Dify工作流运行问题诊断")
    print("=" * 50)
    
    # 检查API服务器
    api_ok = check_api_server()
    
    if api_ok:
        # 测试类目处理
        test_category_processing()
    
    # 检查配置
    check_dify_variables()
    
    # 检查常见问题
    check_common_issues()
    
    # 提供调试步骤
    provide_debug_steps()
    
    print("\n🎯 总结:")
    if api_ok:
        print("✅ API服务器运行正常")
        print("🔍 请检查Dify中的环境变量配置")
        print("📝 尝试使用简单的类目名称测试")
    else:
        print("❌ 请先启动API服务器")
        print("💡 运行: python start_api_server.py")

if __name__ == "__main__":
    main()
