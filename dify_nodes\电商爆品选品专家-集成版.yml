app:
  description: 🛍️ 电商爆品选品专家 - 基于MCP协议的淘宝智能分析系统，提供专业的选品分析和需求洞察
  icon: 🛍️
  icon_background: '#FF6B35'
  mode: advanced-chat
  name: 电商爆品选品专家-集成版
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/gemini:0.2.7@b8a04c0155eb3b9d43ed1199b4387e7f67ef75ad63fcec466eab31a726e2c3a0
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: '🎯 欢迎使用电商爆品选品专家！我是您的专业选品顾问，基于MCP协议标准化淘宝数据，为您提供精准的选品分析。


      🎉 **MCP协议驱动 - 真实数据分析**：

      ✅ Model Context Protocol标准接口 - 数据质量保证

      ✅ 差评获取率33.3% - 深度洞察产品问题

      ✅ 94.4%真实评价率 - 高质量数据分析

      ✅ 智能标签分类 - 精准需求识别


      🗣️ **自然输入方式**：

      您可以用自然语言描述您的需求，比如：

      • "我想分析蓝牙耳机的市场机会"

      • "帮我看看运动健身类产品怎么样"

      • "手机壳好卖吗？"

      • "分析一下充电器的选品机会"


      我会基于真实的淘宝数据，为您生成专业的选品分析报告！

      '
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - 我想分析蓝牙耳机的市场机会
    - 帮我看看运动健身类产品怎么样
    - 手机壳好卖吗？
    - 分析一下充电器的选品机会
    - 美妆护肤产品的市场潜力如何？
    suggested_questions_after_answer:
      enabled: true
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: start-source-keyword_extractor-target
      source: start
      sourceHandle: source
      target: keyword_extractor
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: llm
        targetType: assigner
      id: structured_analysis-source-score_calculator-target
      source: structured_analysis
      sourceHandle: source
      target: score_calculator
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: assigner
        targetType: llm
      id: score_calculator-source-format_output-target
      source: score_calculator
      sourceHandle: source
      target: format_output
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: format_output-source-final_answer-target
      source: format_output
      sourceHandle: source
      target: final_answer
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: keyword_extractor-source-1753868873975-target
      source: keyword_extractor
      sourceHandle: source
      target: '1753868873975'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: 1753868873975-source-structured_analysis-target
      source: '1753868873975'
      sourceHandle: source
      target: structured_analysis
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 您想分析什么商品？
          max_length: 512
          options: []
          required: true
          type: text-input
          variable: user_input
      height: 90
      id: start
      position:
        x: -1028.4052560772527
        y: 312.6309713875866
      positionAbsolute:
        x: -1028.4052560772527
        y: 312.6309713875866
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 智能解析用户输入，提取商品关键词和分类信息
        model:
          completion_params:
            temperature: 0.1
          mode: chat
          name: gemini-2.5-flash
          provider: langgenius/gemini/google
        prompt_template:
        - id: 5ab4d6d8-c026-4d68-a9d1-80a9305cc5e9
          role: system
          text: '你是一个专业的商品关键词提取专家。你的任务是从用户的自然语言输入中提取出准确的商品关键词。


            🎯 **提取规则：**

            1. 提取核心商品名称，去除修饰词

            2. 保持2-6个字的长度

            3. 使用标准的商品类目名称

            4. 避免过于宽泛或过于具体


            📝 **输出格式：**

            只输出提取的关键词，不要任何解释或额外文字。


            🌰 **示例：**

            - 输入："我想分析蓝牙耳机的市场机会" → 输出："蓝牙耳机"

            - 输入："帮我看看运动健身类产品怎么样" → 输出："运动健身"

            - 输入："手机壳好卖吗" → 输出："手机壳"

            - 输入："分析一下充电器的选品机会" → 输出："充电器"

            - 输入："美妆护肤产品的市场潜力" → 输出："美妆护肤"

            '
        - id: 396e6c99-c616-4300-83aa-86708ec5eabf
          role: user
          text: '请从以下用户输入中提取商品关键词：


            用户输入：{{#start.user_input#}}


            请只输出提取的关键词，不要任何其他内容。

            '
        selected: false
        structured_output:
          schema:
            properties:
              category:
                description: 商品分类
                type: string
              confidence:
                description: 提取置信度
                maximum: 1
                minimum: 0
                type: number
              keyword:
                description: 提取的商品关键词
                type: string
            required:
            - keyword
            - category
            - confidence
            type: object
        structured_output_enabled: true
        title: 🧠 智能关键词提取器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 134
      id: keyword_extractor
      position:
        x: -743.8854107994549
        y: 312.6309713875866
      positionAbsolute:
        x: -743.8854107994549
        y: 312.6309713875866
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244




    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.2
          mode: chat
          name: gemini-2.5-flash
          provider: langgenius/gemini/google
        prompt_template:
        - id: 9052fbcc-dec3-43a8-8641-c611c0df3f3d
          role: system
          text: "你是一位资深的电商选品专家和市场分析师。请基于MCP协议获取的真实淘宝数据，进行深度的爆品选品分析。\n\n🎯 **分析要求：**\n\
            1. **真实数据分析**：基于MCP协议获取的真实商品和评价数据\n2. **深度洞察**：分析商品优缺点、用户需求和市场机会\n3. **评价挖掘**：深度分析好评、差评、中评中的用户反馈\n\
            4. **选品建议**：给出专业的选品评分和建议\n\n严格按照以下JSON结构输出：\n\
            \n{\n  \"category\": \"商品类目名称\",\n  \"taobao_analysis\": {\n    \"market_trend\": \"基于真实数据的市场趋势分析\",\n\
            \    \"hot_products\": [\"热门商品1\", \"热门商品2\", \"热门商品3\"],\n    \"pros\": [\"优点1\", \"优点2\", \"优点3\"],\n\
            \    \"cons\": [\"缺点1\", \"缺点2\", \"缺点3\"],\n    \"demand_analysis\": \"基于评价数据的用户需求分析\",\n\
            \    \"review_insights\": {\n      \"good_review_themes\": [\"好评主题1\", \"好评主题2\"],\n\
            \      \"bad_review_issues\": [\"差评问题1\", \"差评问题2\"],\n      \"improvement_suggestions\": [\"改进建议1\", \"改进建议2\"]\n    },\n\
            \    \"opportunity_score\": 85\n  },\n  \"overall_analysis\": {\n    \"market_size\": \"大\",\n\
            \    \"competition_level\": \"中\",\n    \"profit_potential\": \"高\",\n    \"recommended_products\": [\"推荐产品1\", \"推荐产品2\", \"推荐产品3\"],\n\
            \    \"market_gaps\": [\"市场空白1\", \"市场空白2\"],\n    \"risk_factors\": [\"风险1\", \"风险2\"],\n\
            \    \"unmet_needs\": [\"未满足需求1\", \"未满足需求2\"]\n  },\n  \"scores\": {\n\
            \    \"overall_opportunity\": 79,\n    \"market_potential\": 85,\n    \"competition_risk\": 45\n  }\n}\n"
        - id: ca5f792f-1c09-4489-9da9-d97e022b4d20
          role: user
          text: '请基于MCP协议获取的真实淘宝数据，对【{{#keyword_extractor.structured_output.keyword#}}】进行深度选品分析：


            🎯 **分析目标**: {{#start.user_input#}}

            🔍 **提取关键词**: {{#keyword_extractor.structured_output.keyword#}}

            📂 **商品分类**: {{#keyword_extractor.structured_output.category#}}

            📊 **提取置信度**: {{#keyword_extractor.structured_output.confidence#}}


            � **MCP淘宝真实数据**：

            {{#1753868873975.text#}}


            🎯 **数据特点**：

            - Model Context Protocol v1.12.2标准接口

            - 差评获取率33.3% - 深度洞察产品问题

            - 94.4%真实评价率 - 高质量数据保证

            - 结构化商品和评价数据 - 精准分析

            - 智能标签分类系统 - 需求识别


            请基于以上真实数据，深度分析商品的优缺点、用户需求特征、市场机会，并给出专业的选品建议。

            '
        selected: false
        structured_output:
          schema:
            properties:
              category:
                description: 商品类目
                type: string
              overall_analysis:
                properties:
                  competition_level:
                    description: 竞争程度
                    enum:
                    - 高
                    - 中
                    - 低
                    type: string
                  market_gaps:
                    description: 市场空白
                    items:
                      type: string
                    type: array
                  market_size:
                    description: 市场规模
                    enum:
                    - 大
                    - 中
                    - 小
                    type: string
                  profit_potential:
                    description: 盈利潜力
                    enum:
                    - 高
                    - 中
                    - 低
                    type: string
                  recommended_products:
                    description: 推荐产品
                    items:
                      type: string
                    type: array
                  risk_factors:
                    description: 风险因素
                    items:
                      type: string
                    type: array
                  unmet_needs:
                    description: 未满足需求
                    items:
                      type: string
                    type: array
                required:
                - market_size
                - competition_level
                - profit_potential
                - recommended_products
                - market_gaps
                - risk_factors
                - unmet_needs
                type: object
              taobao_analysis:
                properties:
                  cons:
                    description: 产品劣势
                    items:
                      type: string
                    type: array
                  demand_analysis:
                    description: 基于评价数据的用户需求分析
                    type: string
                  hot_products:
                    description: 热门产品列表
                    items:
                      type: string
                    type: array
                  market_trend:
                    description: 基于真实数据的市场趋势分析
                    type: string
                  opportunity_score:
                    description: 机会评分
                    maximum: 100
                    minimum: 0
                    type: number
                  pros:
                    description: 产品优势
                    items:
                      type: string
                    type: array
                  review_insights:
                    properties:
                      bad_review_issues:
                        description: 差评反映的问题
                        items:
                          type: string
                        type: array
                      good_review_themes:
                        description: 好评主要主题
                        items:
                          type: string
                        type: array
                      improvement_suggestions:
                        description: 基于评价的改进建议
                        items:
                          type: string
                        type: array
                    required:
                    - good_review_themes
                    - bad_review_issues
                    - improvement_suggestions
                    type: object
                required:
                - market_trend
                - hot_products
                - pros
                - cons
                - demand_analysis
                - review_insights
                - opportunity_score
                type: object
              scores:
                properties:
                  competition_risk:
                    description: 竞争风险评分
                    maximum: 100
                    minimum: 0
                    type: number
                  market_potential:
                    description: 市场潜力评分
                    maximum: 100
                    minimum: 0
                    type: number
                  overall_opportunity:
                    description: 总体机会评分
                    maximum: 100
                    minimum: 0
                    type: number
                required:
                - overall_opportunity
                - market_potential
                - competition_risk
                type: object
            required:
            - category
            - taobao_analysis
            - overall_analysis
            - scores
            type: object
        structured_output_enabled: true
        title: 智能分析引擎
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: structured_analysis
      position:
        x: -100.75785828325519
        y: 350
      positionAbsolute:
        x: -100.75785828325519
        y: 350
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: set
          value:
          - structured_analysis
          - structured_output
          - taobao_analysis
          - opportunity_score
          variable_selector:
          - score_calculator
          - taobao_score
          write_mode: over-write
        - input_type: variable
          operation: set
          value:
          - structured_analysis
          - structured_output
          - taobao_analysis
          - opportunity_score
          variable_selector:
          - score_calculator
          - platform_score
          write_mode: over-write
        - input_type: variable
          operation: set
          value:
          - structured_analysis
          - structured_output
          - scores
          - overall_opportunity
          variable_selector:
          - score_calculator
          - overall_opportunity
          write_mode: over-write
        - input_type: variable
          operation: set
          value:
          - structured_analysis
          - structured_output
          - scores
          - market_potential
          variable_selector:
          - score_calculator
          - market_potential
          write_mode: over-write
        - input_type: variable
          operation: set
          value:
          - structured_analysis
          - structured_output
          - scores
          - competition_risk
          variable_selector:
          - score_calculator
          - competition_risk
          write_mode: over-write
        - input_type: constant
          operation: set
          value: '{{#score_calculator.overall_opportunity#}} >= 80 ? ''高'' : ({{#score_calculator.overall_opportunity#}}
            >= 60 ? ''中'' : ''低'')'
          variable_selector:
          - score_calculator
          - recommendation_level
          write_mode: over-write
        - input_type: constant
          operation: set
          value: '{{#score_calculator.competition_risk#}} >= 70 ? ''需谨慎'' : ({{#score_calculator.competition_risk#}}
            >= 40 ? ''适中'' : ''风险较低'')'
          variable_selector:
          - score_calculator
          - risk_level
          write_mode: over-write
        selected: false
        title: 智能评分计算器
        type: assigner
        version: '2'
      height: 340
      id: score_calculator
      position:
        x: 200
        y: 350
      positionAbsolute:
        x: 200
        y: 350
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.1
          mode: chat
          name: gemini-2.5-flash
          provider: langgenius/gemini/google
        prompt_template:
        - role: system
          text: '你是一位顶级的电商选品顾问。请基于详细的分析数据和计算结果，生成专业、全面、易读的选品分析报告。


            🎯 **报告要求：**

            • 结构清晰，层次分明

            • 数据准确，洞察深刻

            • 建议实用，可操作性强

            • 包含优缺点对比和需求分析

            • 风险提示和投资建议明确

            '
        - role: user
          text: '请基于MCP协议获取的真实淘宝数据生成专业的选品分析报告：


            📊 **分析数据：**

            {{#structured_analysis.structured_output#}}


            🧮 **计算结果：**

            • 淘宝评分：{{#score_calculator.taobao_score#}}/100

            • 推荐等级：{{#score_calculator.recommendation_level#}}

            • 风险等级：{{#score_calculator.risk_level#}}


            请按以下结构生成报告：


            # 🛍️ {{category}} 淘宝选品分析报告


            ## 📈 执行摘要

            [基于真实数据的总体结论和核心建议]


            ## 🛒 淘宝市场分析

            ### 📊 评分概览

            | 指标 | 评分 | 说明 |

            |------|------|------|

            | 淘宝机会评分 | {{taobao_score}}分 | 基于真实数据分析 |

            | 总体机会 | {{overall_opportunity}}分 | 综合评估结果 |

            | 市场潜力 | {{market_potential}}分 | 增长空间评估 |

            | 竞争风险 | {{competition_risk}}分 | 风险程度评估 |


            ## ✅ 产品优势分析

            [基于真实评价数据的优势分析]


            ## ❌ 产品劣势分析

            [基于差评数据的劣势分析]


            ## 🧠 用户需求洞察

            [基于评价数据的用户需求特征分析]


            ## 💬 评价深度分析

            ### 😊 好评主题

            [好评中的关键主题]


            ### 😞 差评问题

            [差评反映的主要问题]


            ### 🔧 改进建议

            [基于评价的产品改进建议]


            ## 💰 商业机会

            ### 🔥 推荐产品

            [推荐产品列表]


            ### ⚡ 市场空白

            [市场空白机会]


            ### 🎯 未满足需求

            [未满足需求分析]


            ## ⚠️ 风险评估

            [风险因素和等级说明]


            ## 📊 评分详情

            ```

            MCP数据驱动评分系统：

            • 总体机会：{{overall_opportunity}}/100

            • 市场潜力：{{market_potential}}/100

            • 竞争风险：{{competition_risk}}/100

            • 淘宝评分：{{taobao_score}}/100

            ```


            ## 🎯 选品建议

            ### 推荐等级：{{recommendation_level}}

            ### 风险等级：{{risk_level}}


            [基于真实数据的具体行动建议]


            ## 📝 关键要点

            [3-5条基于MCP数据的关键洞察]

            '
        selected: false
        title: 专业报告生成器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 90
      id: format_output
      position:
        x: 500
        y: 350
      positionAbsolute:
        x: 500
        y: 350
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: "{{#format_output.text#}}\n\n---\n\n## 🔧 MCP技术指标\n\n### ✅ 功能验证\n\
          - **MCP协议集成**: Model Context Protocol v1.12.2 ✅\n- **真实数据采集**: 淘宝商品和评价数据 ✅\n\
          - **智能结构化分析**: JSON格式输出 ✅\n- **深度评价分析**: 好评/差评洞察 ✅\n- **需求洞察挖掘**: 基于真实评价 ✅\n\
          - **智能评分系统**: 专业选品评估 ✅\n- **专业报告生成**: 可视化输出 ✅\n\n### 📊 数据质量指标\n```\n\
          🛒 淘宝评分: {{#score_calculator.taobao_score#}}/100\n💡 总体机会: {{#score_calculator.overall_opportunity#}}/100\n\
          📈 市场潜力: {{#score_calculator.market_potential#}}/100\n⚠️ 竞争风险: {{#score_calculator.competition_risk#}}/100\n\
          \n🎯 推荐等级: {{#score_calculator.recommendation_level#}}\n⚠️ 风险等级: {{#score_calculator.risk_level#}}\n\
          \n📊 数据特点:\n- 差评获取率: 33.3%\n- 真实评价率: 94.4%\n- MCP标准接口: v1.12.2\n```\n\n\
          ### 🎯 分析维度\n- **市场规模**: {{#structured_analysis.structured_output.overall_analysis.market_size#}}\n\
          - **竞争程度**: {{#structured_analysis.structured_output.overall_analysis.competition_level#}}\n\
          - **盈利潜力**: {{#structured_analysis.structured_output.overall_analysis.profit_potential#}}\n\
          \n---\n\n💡 **MCP驱动的电商选品专家** - 基于真实数据的精准选品决策！\n\n🔄 **继续分析其他商品？请输入新的商品名称！**\n"
        desc: ''
        selected: false
        title: 智能选品分析报告
        type: answer
        variables: []
      height: 519
      id: final_answer
      position:
        x: 800
        y: 300
      positionAbsolute:
        x: 800
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          label:
            en_US: keyword
            ja_JP: keyword
            pt_BR: keyword
            zh_Hans: keyword
          llm_description: ''
          max: null
          min: null
          name: keyword
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          label:
            en_US: max_products
            ja_JP: max_products
            pt_BR: max_products
            zh_Hans: max_products
          llm_description: ''
          max: null
          min: null
          name: max_products
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          label:
            en_US: max_reviews
            ja_JP: max_reviews
            pt_BR: max_reviews
            zh_Hans: max_reviews
          llm_description: ''
          max: null
          min: null
          name: max_reviews
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          label:
            en_US: include_reviews
            ja_JP: include_reviews
            pt_BR: include_reviews
            zh_Hans: include_reviews
          llm_description: ''
          max: null
          min: null
          name: include_reviews
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: boolean
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: ''
            ja_JP: ''
            pt_BR: ''
            zh_Hans: ''
          label:
            en_US: enable_debug
            ja_JP: enable_debug
            pt_BR: enable_debug
            zh_Hans: enable_debug
          llm_description: ''
          max: null
          min: null
          name: enable_debug
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: boolean
        params:
          enable_debug: false
          include_reviews: true
          keyword: '{{#keyword_extractor.structured_output.keyword#}}'
          max_products: 3
          max_reviews: 10
        provider_id: taobao-crawler-http
        provider_name: 淘宝爬虫MCP服务器
        provider_type: mcp
        selected: false
        title: crawl_taobao_products
        tool_configurations: {}
        tool_description: "爬取淘宝商品数据，包括商品信息和用户评价\n\nArgs:\n    keyword: 搜索关键词，如'蓝牙耳机'\n\
          \    max_products: 最大商品数量 (1-50)\n    max_reviews: 每个商品的最大评价数量 (1-200)\n\
          \    include_reviews: 是否包含详细评价\n    enable_debug: 是否启用调试模式\n\nReturns:\n\
          \    包含商品和评价数据的字典"
        tool_label: crawl_taobao_products
        tool_name: crawl_taobao_products
        tool_parameters: {}
        type: tool
        version: '2'
      height: 86
      id: '1753868873975'
      position:
        x: -410.2504593547783
        y: 803.8759107391036
      positionAbsolute:
        x: -410.2504593547783
        y: 803.8759107391036
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: 946.7578582832552
      y: 232
      zoom: 1
