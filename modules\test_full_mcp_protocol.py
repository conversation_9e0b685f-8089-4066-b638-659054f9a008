#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整MCP协议功能
使用官方MCP客户端连接到淘宝爬虫MCP服务器
"""

import asyncio
import sys
import os
import subprocess

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_full_mcp_protocol():
    """测试完整MCP协议功能"""
    print("🚀 测试完整MCP协议功能")
    print("=" * 60)
    
    # 创建服务器参数
    server_params = StdioServerParameters(
        command="python",
        args=["taobao_mcp_server.py"],
        env=os.environ.copy()
    )
    
    try:
        print("📡 连接到MCP服务器...")
        async with stdio_client(server_params) as (read_stream, write_stream):
            async with ClientSession(read_stream, write_stream) as session:
                print("✅ MCP客户端会话建立成功")
                
                # 初始化连接
                print("\n🔧 初始化MCP连接...")
                await session.initialize()
                print("✅ MCP连接初始化成功")
                
                # 列出可用工具
                print("\n🛠️  列出可用工具...")
                tools_response = await session.list_tools()
                print(f"发现 {len(tools_response.tools)} 个工具:")
                for tool in tools_response.tools:
                    print(f"  - {tool.name}: {tool.description}")
                
                # 测试工具调用
                if tools_response.tools:
                    print("\n🔍 测试工具调用...")
                    tool_name = "crawl_taobao_products"
                    test_args = {
                        'keyword': '无线鼠标',
                        'max_products': 1,
                        'max_reviews': 3,
                        'include_reviews': True,
                        'enable_debug': False
                    }
                    
                    print(f"调用工具: {tool_name}")
                    print(f"参数: {test_args}")
                    
                    result = await session.call_tool(tool_name, arguments=test_args)
                    
                    print("✅ 工具调用成功")
                    print(f"返回内容类型: {type(result.content[0]).__name__}")
                    
                    # 解析结果
                    if result.content:
                        content = result.content[0]
                        if hasattr(content, 'text'):
                            import json
                            try:
                                data = json.loads(content.text)
                                if data.get('success'):
                                    summary = data.get('data', {}).get('summary', {})
                                    print(f"📊 爬取结果: {summary.get('total_products', 0)}个商品, {summary.get('total_reviews', 0)}条评价")
                                else:
                                    print(f"❌ 爬取失败: {data.get('error', '未知错误')}")
                            except json.JSONDecodeError:
                                print("📄 返回非JSON格式数据")
                
                # 测试状态工具
                print("\n📊 测试状态工具...")
                try:
                    status_result = await session.call_tool("get_crawler_status", arguments={})
                    print("✅ 状态工具调用成功")
                    if status_result.content:
                        content = status_result.content[0]
                        if hasattr(content, 'text'):
                            import json
                            try:
                                status_data = json.loads(content.text)
                                print(f"服务状态: {status_data.get('status', 'unknown')}")
                                print(f"服务版本: {status_data.get('version', 'unknown')}")
                            except json.JSONDecodeError:
                                print("状态数据解析失败")
                except Exception as e:
                    print(f"⚠️  状态工具调用失败: {e}")
                
                print("\n✅ 完整MCP协议测试完成")
                print("🎉 所有MCP功能正常工作！")
                
    except Exception as e:
        print(f"❌ MCP协议测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_mcp_inspector():
    """测试MCP Inspector工具"""
    print("\n🔍 测试MCP Inspector...")
    print("=" * 40)
    
    try:
        # 检查mcp命令是否可用
        result = subprocess.run(['mcp', '--help'], 
                              capture_output=True, 
                              text=True, 
                              timeout=10)
        
        if result.returncode == 0:
            print("✅ MCP CLI工具可用")
            print("💡 你可以使用以下命令测试服务器:")
            print("   mcp dev taobao_mcp_server.py")
            print("   这将启动MCP Inspector进行交互式测试")
        else:
            print("⚠️  MCP CLI工具不可用")
            
    except subprocess.TimeoutExpired:
        print("⚠️  MCP CLI命令超时")
    except FileNotFoundError:
        print("⚠️  未找到mcp命令，可能需要重新安装")
    except Exception as e:
        print(f"⚠️  MCP CLI测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_full_mcp_protocol())
    asyncio.run(test_mcp_inspector())
