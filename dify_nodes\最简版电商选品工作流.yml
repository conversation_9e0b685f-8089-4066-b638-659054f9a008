app:
  description: 电商爆品选品工作流 - 最简版
  icon: 🛍️
  icon_background: '#FF6B35'
  mode: advanced-chat
  name: 电商爆品选品工作流
  use_icon_as_answer_icon: false

dependencies: []
kind: app
version: 0.3.0

workflow:
  conversation_variables: []
  environment_variables: []
  
  features:
    file_upload:
      enabled: false
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false

  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: start-source-simple_analysis-target
      source: start
      sourceHandle: source
      target: simple_analysis
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: simple_analysis-source-final_answer-target
      source: simple_analysis
      sourceHandle: source
      target: final_answer
      targetHandle: target
      type: custom

    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 商品类目
          max_length: 256
          options: []
          required: true
          type: text-input
          variable: category
      height: 116
      id: start
      position:
        x: -300
        y: 300
      positionAbsolute:
        x: -300
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.3
            max_tokens: 1024
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        prompt_template:
        - role: system
          text: |
            你是一位资深的电商选品专家。请基于用户提供的商品类目，给出简要的市场分析和选品建议。
            
            请用以下格式回答：
            
            ## 📊 商品类目分析
            
            ### 市场概况
            [简要描述该类目的市场现状]
            
            ### 热门趋势
            [列出3-5个当前热门趋势]
            
            ### 选品建议
            [提供3-5个具体的选品建议]
            
            ### 注意事项
            [提醒需要注意的要点]
        - role: user
          text: |
            请分析 "{{#start.category#}}" 这个商品类目的市场情况和选品机会。
        selected: false
        title: 简单商品分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 96
      id: simple_analysis
      position:
        x: 0
        y: 300
      positionAbsolute:
        x: 0
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        answer: |
          {{#simple_analysis.text#}}
          
          ---
          
          💡 **提示**: 这是一个基础的选品分析。如果您需要更详细的多平台数据分析，可以考虑使用我们的完整版工作流！
        desc: ''
        selected: false
        title: 分析结果
        type: answer
        variables: []
      height: 185
      id: final_answer
      position:
        x: 300
        y: 300
      positionAbsolute:
        x: 300
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    viewport:
      x: 300
      y: -300
      zoom: 1.0 