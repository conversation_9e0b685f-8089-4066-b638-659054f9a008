#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
淘宝爬虫MCP服务器
使用Model Context Protocol提供淘宝商品数据爬取服务
"""

import asyncio
import json
import logging
import sys
import os
from typing import Any, Dict, List, Optional

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# MCP相关导入
from mcp.server.lowlevel import Server, NotificationOptions
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import Tool, TextContent

# 导入最终版爬虫
try:
    from final_taobao_review_crawler import FinalTaobaoReviewCrawler
except ImportError:
    print("❌ 无法导入最终版爬虫，请确保文件存在")
    sys.exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("taobao-mcp-server")

# 全局爬虫实例
crawler: Optional[FinalTaobaoReviewCrawler] = None

class TaobaoMCPServer:
    """淘宝爬虫MCP服务器"""

    def __init__(self):
        self.name = "taobao-crawler"
        self.version = "3.0.0"

        # 初始化MCP服务器
        self.server = Server(self.name)
        self.setup_handlers()
    
    def setup_handlers(self):
        """设置MCP处理器"""
        
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """列出可用的工具"""
            return [
                Tool(
                    name="crawl_taobao_products",
                    description="爬取淘宝商品数据，包括商品信息和用户评价",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "keyword": {
                                "type": "string",
                                "description": "搜索关键词，如'蓝牙耳机'"
                            },
                            "max_products": {
                                "type": "integer",
                                "description": "最大商品数量",
                                "default": 10,
                                "minimum": 1,
                                "maximum": 50
                            },
                            "max_reviews": {
                                "type": "integer",
                                "description": "每个商品的最大评价数量",
                                "default": 60,
                                "minimum": 1,
                                "maximum": 200
                            },
                            "include_reviews": {
                                "type": "boolean",
                                "description": "是否包含详细评价",
                                "default": True
                            },
                            "enable_debug": {
                                "type": "boolean",
                                "description": "是否启用调试模式",
                                "default": False
                            }
                        },
                        "required": ["keyword"]
                    }
                ),
                Tool(
                    name="get_crawler_status",
                    description="获取爬虫状态和版本信息",
                    inputSchema={
                        "type": "object",
                        "properties": {},
                        "additionalProperties": False
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """处理工具调用"""
            
            if name == "crawl_taobao_products":
                return await self.crawl_products(arguments)
            elif name == "get_crawler_status":
                return await self.get_status()
            else:
                raise ValueError(f"未知工具: {name}")
    
    async def crawl_products(self, args: Dict[str, Any]) -> List[TextContent]:
        """爬取淘宝商品"""
        global crawler
        
        try:
            # 初始化爬虫
            if not crawler:
                crawler = FinalTaobaoReviewCrawler(headless=True, debug=args.get('enable_debug', False))
                logger.info("爬虫初始化成功")

            # 确保driver已创建并登录
            if not crawler.driver:
                crawler._create_driver()
                logger.info("WebDriver创建成功")

            if not crawler.is_logged_in:
                login_success = crawler.login_with_cookies()
                if login_success:
                    logger.info("Cookies登录成功")
                else:
                    logger.warning("Cookies登录失败，将尝试无登录模式")
            
            # 提取参数
            keyword = args.get('keyword')
            max_products = args.get('max_products', 10)
            max_reviews = args.get('max_reviews', 60)
            include_reviews = args.get('include_reviews', True)
            
            logger.info(f"开始爬取: 关键词={keyword}, 商品数={max_products}, 评价数={max_reviews}")
            
            # 执行爬取
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: crawler.get_products_with_reviews(
                    keyword=keyword,
                    max_results=max_products,
                    max_reviews_per_product=max_reviews
                )
            )
            
            if result and isinstance(result, list) and len(result) > 0:
                logger.info(f"爬取成功: {len(result)}个商品")

                # 统计评价数量
                total_reviews = sum(
                    len(product.good_reviews) + len(product.bad_reviews) + len(product.medium_reviews)
                    for product in result
                )

                # 转换为可序列化的格式
                products_data = []
                for product in result:
                    product_dict = {
                        "title": product.title,
                        "price": product.price,
                        "sales": product.sales,
                        "shop": product.shop,
                        "location": product.location,
                        "detail_url": product.detail_url,
                        "good_reviews": [{"text": r.review_text, "type": r.review_type, "tags": r.tags} for r in product.good_reviews],
                        "bad_reviews": [{"text": r.review_text, "type": r.review_type, "tags": r.tags} for r in product.bad_reviews],
                        "medium_reviews": [{"text": r.review_text, "type": r.review_type, "tags": r.tags} for r in product.medium_reviews]
                    }
                    products_data.append(product_dict)

                # 格式化响应
                response_data = {
                    "success": True,
                    "data": {
                        "products": products_data,
                        "summary": {
                            "total_products": len(result),
                            "total_reviews": total_reviews,
                            "keyword": keyword
                        }
                    },
                    "message": f"成功爬取{len(result)}个商品，{total_reviews}条评价",
                    "crawler_info": {
                        "version": "v3.0 Final MCP",
                        "protocol": "Model Context Protocol",
                        "features": [
                            "智能关键词提取",
                            "基于标签的评价分类",
                            "容器内精准滚动",
                            "真实评价验证过滤",
                            "差评获取突破",
                            "MCP标准化接口"
                        ]
                    }
                }

                return [TextContent(
                    type="text",
                    text=json.dumps(response_data, ensure_ascii=False, indent=2)
                )]
            else:
                error_msg = '爬取失败或未获取到商品数据'
                logger.error(f"爬取失败: {error_msg}")

                return [TextContent(
                    type="text",
                    text=json.dumps({
                        "success": False,
                        "error": error_msg,
                        "message": "爬取失败，请检查网络连接和cookies状态"
                    }, ensure_ascii=False, indent=2)
                )]
                
        except Exception as e:
            logger.error(f"爬取异常: {e}")
            return [TextContent(
                type="text",
                text=json.dumps({
                    "success": False,
                    "error": str(e),
                    "message": "爬取过程中发生异常"
                }, ensure_ascii=False, indent=2)
            )]
    
    async def get_status(self) -> List[TextContent]:
        """获取爬虫状态"""
        global crawler
        
        status_data = {
            "service": "淘宝爬虫MCP服务器",
            "version": "v3.0 Final MCP",
            "protocol": "Model Context Protocol",
            "status": "healthy",
            "crawler_ready": crawler is not None,
            "features": [
                "智能关键词提取",
                "基于标签的评价分类", 
                "容器内精准滚动",
                "真实评价验证过滤",
                "差评获取突破",
                "MCP标准化接口"
            ],
            "capabilities": {
                "max_products": 50,
                "max_reviews_per_product": 200,
                "supported_platforms": ["淘宝"],
                "output_formats": ["JSON", "结构化数据"]
            }
        }
        
        return [TextContent(
            type="text",
            text=json.dumps(status_data, ensure_ascii=False, indent=2)
        )]
    
    async def run(self):
        """运行MCP服务器"""
        logger.info("启动淘宝爬虫MCP服务器...")

        # 初始化爬虫
        global crawler
        try:
            crawler = FinalTaobaoReviewCrawler(headless=True, debug=False)
            logger.info("爬虫初始化成功")
        except Exception as e:
            logger.warning(f"爬虫初始化失败: {e}")

        # 运行服务器
        try:
            async with stdio_server() as (read_stream, write_stream):
                await self.server.run(
                    read_stream,
                    write_stream,
                    InitializationOptions(
                        server_name="taobao-crawler",
                        server_version="3.0.0",
                        capabilities=self.server.get_capabilities(
                            notification_options=NotificationOptions(),
                            experimental_capabilities={}
                        )
                    )
                )
        except Exception as e:
            logger.error(f"MCP服务器运行失败: {e}")
            # 如果MCP失败，提供简化的测试模式
            await self.run_simple_test()

    async def run_simple_test(self):
        """运行简化测试模式"""
        logger.info("启动简化测试模式...")

        # 测试爬虫状态
        status_result = await self.get_status()
        print("=== 爬虫状态 ===")
        print(status_result[0].text)

        # 测试简单爬取
        print("\n=== 测试爬取 ===")
        test_args = {
            'keyword': '蓝牙耳机',
            'max_products': 2,
            'max_reviews': 10,
            'include_reviews': True,
            'enable_debug': True
        }

        crawl_result = await self.crawl_products(test_args)
        print(crawl_result[0].text)

async def main():
    """主函数"""
    server = TaobaoMCPServer()
    await server.run()

if __name__ == "__main__":
    asyncio.run(main())
