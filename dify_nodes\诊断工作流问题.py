#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断Dify工作流执行问题
分析为什么关键词提取后没有进入淘宝爬虫
"""

import requests
import json
import time

def test_keyword_extraction():
    """测试关键词提取功能"""
    print("🧠 测试关键词提取功能...")
    
    # 模拟关键词提取器的输出
    extracted_data = {
        "keyword": "蓝牙耳机",
        "category": "消费电子",
        "confidence": 1.0
    }
    
    print(f"✅ 关键词提取成功:")
    print(f"   关键词: {extracted_data['keyword']}")
    print(f"   分类: {extracted_data['category']}")
    print(f"   置信度: {extracted_data['confidence']}")
    
    return extracted_data

def test_api_with_extracted_keyword(keyword):
    """使用提取的关键词测试API"""
    print(f"\n🔍 测试API - 关键词: {keyword}")
    
    # 检查API健康状态
    try:
        health_response = requests.get("http://localhost:8000/api/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ API服务器健康检查通过")
        else:
            print(f"❌ API健康检查失败: {health_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接API服务器: {e}")
        return False
    
    # 测试爬取接口
    test_data = {
        "keyword": keyword,
        "max_products": 2,
        "max_reviews": 10,
        "include_reviews": True,
        "enable_debug": True
    }
    
    print(f"📤 发送爬取请求: {json.dumps(test_data, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/crawl",
            json=test_data,
            timeout=60
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('success'):
                    print("✅ 淘宝爬虫执行成功")
                    print(f"   商品数: {data['data']['summary']['total_products']}")
                    print(f"   评价数: {data['data']['summary']['total_reviews']}")
                    return True
                else:
                    print(f"❌ 爬取失败: {data.get('error')}")
                    return False
            except Exception as e:
                print(f"❌ 响应解析失败: {e}")
                print(f"   响应内容: {response.text[:200]}...")
                return False
        else:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_dify_configuration():
    """检查Dify配置"""
    print("\n🔧 Dify配置检查...")
    
    print("📋 必需的环境变量:")
    print("   变量名: TAOBAO_CRAWLER_API_URL")
    print("   变量值: http://localhost:8000/api/crawl")
    
    print("\n📝 数据引用格式:")
    print("   关键词引用: {{#keyword_extractor.structured_output.keyword#}}")
    print("   分类引用: {{#keyword_extractor.structured_output.category#}}")
    print("   置信度引用: {{#keyword_extractor.structured_output.confidence#}}")
    
    print("\n🔗 节点连接顺序:")
    print("   1. START (用户输入)")
    print("   2. 🧠 智能关键词提取器")
    print("   3. 🎯 最终版淘宝爬虫")
    print("   4. 其他搜索工具...")
    print("   5. 🧠 智能分析引擎")

def analyze_possible_issues():
    """分析可能的问题"""
    print("\n🔍 可能的问题分析...")
    
    issues = [
        {
            "问题": "工作流在关键词提取后停止",
            "可能原因": [
                "节点连接配置错误",
                "数据引用格式不正确",
                "环境变量未配置",
                "API服务器502错误"
            ],
            "检查方法": [
                "查看Dify工作流执行日志",
                "检查节点连接线是否正确",
                "验证环境变量配置",
                "测试API服务器状态"
            ]
        },
        {
            "问题": "淘宝爬虫节点执行失败",
            "可能原因": [
                "API服务器未启动",
                "Cookies过期",
                "网络连接问题",
                "请求参数格式错误"
            ],
            "解决方案": [
                "重启API服务器",
                "重新获取淘宝cookies",
                "检查网络连接",
                "验证请求参数格式"
            ]
        }
    ]
    
    for i, issue in enumerate(issues, 1):
        print(f"\n{i}. {issue['问题']}")
        print("   可能原因:")
        for reason in issue['可能原因']:
            print(f"     - {reason}")
        if '检查方法' in issue:
            print("   检查方法:")
            for method in issue['检查方法']:
                print(f"     🔍 {method}")
        if '解决方案' in issue:
            print("   解决方案:")
            for solution in issue['解决方案']:
                print(f"     ✅ {solution}")

def provide_solutions():
    """提供解决方案"""
    print("\n💡 解决方案建议...")
    
    solutions = [
        {
            "方案": "临时使用模拟数据",
            "步骤": [
                "修改淘宝节点URL为: https://httpbin.org/json",
                "修改请求方法为: GET",
                "添加headers包含关键词信息",
                "验证工作流能否完整执行"
            ],
            "优点": "可以立即验证工作流逻辑"
        },
        {
            "方案": "修复API服务器",
            "步骤": [
                "重新获取淘宝cookies: python login.py",
                "重启API服务器: python start_api_server.py",
                "测试API接口: python test_api_direct.py",
                "确认API正常后恢复真实配置"
            ],
            "优点": "获得真实的淘宝数据"
        },
        {
            "方案": "检查Dify配置",
            "步骤": [
                "确认环境变量TAOBAO_CRAWLER_API_URL已配置",
                "检查节点连接线是否正确",
                "查看Dify执行日志中的错误信息",
                "逐个节点测试执行"
            ],
            "优点": "找到根本问题所在"
        }
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"\n方案{i}: {solution['方案']}")
        print("   执行步骤:")
        for step in solution['步骤']:
            print(f"     {step}")
        print(f"   优点: {solution['优点']}")

def main():
    """主函数"""
    print("🔍 Dify工作流执行问题诊断")
    print("=" * 60)
    print("问题: 关键词提取成功，但没有进入淘宝爬虫处理")
    print("=" * 60)
    
    # 测试关键词提取
    extracted_data = test_keyword_extraction()
    
    # 测试API
    api_ok = test_api_with_extracted_keyword(extracted_data['keyword'])
    
    # 检查配置
    check_dify_configuration()
    
    # 分析问题
    analyze_possible_issues()
    
    # 提供解决方案
    provide_solutions()
    
    print("\n🎯 诊断总结:")
    print(f"✅ 关键词提取: 正常")
    print(f"{'✅' if api_ok else '❌'} API服务器: {'正常' if api_ok else '异常'}")
    print(f"🔍 建议: {'检查Dify节点连接和环境变量配置' if api_ok else '先修复API服务器问题'}")

if __name__ == "__main__":
    main()
