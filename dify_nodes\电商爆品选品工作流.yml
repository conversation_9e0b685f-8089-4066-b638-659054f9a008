app:
  description: 智能电商爆品选品系统：自动抓取抖音/淘宝/亚马逊/拼多多热门商品数据，使用AI分析产品优缺点、市场需求和竞争机会，生成详细的选品报告
  icon: 🛍️
  icon_background: '#FF6B35'
  mode: advanced-chat
  name: 电商爆品选品智能分析系统
  use_icon_as_answer_icon: false

dependencies: []
kind: app
version: 0.3.0

workflow:
  features:
    file_upload:
      allowed_file_extensions:
        - .JPG
        - .JPEG
        - .PNG
        - .GIF
        - .WEBP
        - .SVG
        - .PDF
        - .TXT
        - .DOCX
        - .XLSX
      allowed_file_types:
        - image
        - document
      allowed_file_upload_methods:
        - local_file
        - remote_url
      enabled: true
      image:
        enabled: true
        number_limits: 5
        transfer_methods:
          - local_file
          - remote_url
      number_limits: 10
    opening_statement: |
      👋 欢迎使用电商爆品选品智能分析系统！
      
      我可以帮您：
      🔍 分析特定类目的热门商品
      📊 提供详细的优缺点分析
      💡 识别市场机会和用户需求
      📈 生成专业的选品报告
      
      请告诉我您想分析的商品类目（如：家居服、数码配件、美妆护肤等），我将为您提供全面的市场分析！
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
      - "分析一下家居服类目的爆品趋势"
      - "帮我找找数码配件的市场机会"
      - "美妆护肤品有什么值得关注的产品"
      - "运动健身类目的热门商品分析"
    suggested_questions_after_answer:
      enabled: true
    text_to_speech:
      enabled: false

  environment_variables:
    - key: MCP_ENDPOINT
      value: http://localhost:8000
      description: MCP服务器统一接入地址
    - key: ANALYSIS_DEPTH
      value: detailed
      description: 分析深度配置：basic/detailed/comprehensive

  conversation_variables:
    - id: category
      name: 商品类目
      description: 用户指定的商品分析类目
      value_type: string
    - id: platform_preference
      name: 平台偏好
      description: 用户偏好的电商平台
      value_type: string

  graph:
    nodes:
      # ========== 1. 工作流启动节点 ==========
      - id: start
        type: start
        data:
          title: 🚀 启动分析
          type: start
          variables:
            - variable: category
              type: string
              label: 商品类目
              max_length: 50
              options: []
              description: 请输入要分析的商品类目
              required: true

      # ========== 2. 类目验证与处理节点 ==========
      - id: category_processor
        type: code
        data:
          title: 🔍 类目处理
          code: |
            import re
            
            def main(category: str) -> dict:
                # 清理和标准化类目名称
                cleaned_category = re.sub(r'[^\w\s-]', '', category.strip())
                
                # 英文翻译映射
                category_mapping = {
                    '家居服': 'home clothes',
                    '数码配件': 'digital accessories', 
                    '美妆护肤': 'beauty skincare',
                    '运动健身': 'sports fitness',
                    '母婴用品': 'baby products',
                    '厨房用品': 'kitchen supplies',
                    '服装鞋帽': 'clothing shoes',
                    '汽车用品': 'car accessories'
                }
                
                english_category = category_mapping.get(cleaned_category, cleaned_category)
                
                return {
                    'chinese_category': cleaned_category,
                    'english_category': english_category,
                    'search_ready': True
                }
          outputs:
            chinese_category:
              type: string
            english_category: 
              type: string
            search_ready:
              type: boolean
        upstream: [start]

      # ========== 3. 并行平台数据抓取节点 ==========
      - id: crawl_douyin
        type: tool
        data:
          title: 📱 抖音热榜抓取
          tool_name: douyin_top20
          tool_provider: mcp
          parameters:
            keyword: "{{ category_processor.chinese_category }}"
            limit: 20
            sort_by: popularity
        upstream: [category_processor]

      - id: crawl_taobao
        type: tool
        data:
          title: 🛒 淘宝爆品抓取
          tool_name: taobao_top20
          tool_provider: mcp
          parameters:
            keyword: "{{ category_processor.chinese_category }}"
            limit: 20
            sort_by: sales_volume
        upstream: [category_processor]

      - id: crawl_amazon
        type: tool
        data:
          title: 📦 Amazon热品抓取
          tool_name: amazon_top20
          tool_provider: mcp
          parameters:
            keyword: "{{ category_processor.english_category }}"
            limit: 20
            marketplace: US
            sort_by: best_sellers
        upstream: [category_processor]

      - id: crawl_pdd
        type: tool
        data:
          title: 🔥 拼多多热销抓取
          tool_name: pdd_top20
          tool_provider: mcp
          parameters:
            keyword: "{{ category_processor.chinese_category }}"
            limit: 20
            sort_by: order_count
        upstream: [category_processor]

      # ========== 4. 数据聚合节点 ==========
      - id: data_aggregator
        type: code
        data:
          title: 📊 数据聚合处理
          code: |
            import json
            
            def main(douyin_data: str, taobao_data: str, amazon_data: str, pdd_data: str) -> dict:
                platforms_data = {
                    'douyin': json.loads(douyin_data) if douyin_data else [],
                    'taobao': json.loads(taobao_data) if taobao_data else [],
                    'amazon': json.loads(amazon_data) if amazon_data else [],
                    'pdd': json.loads(pdd_data) if pdd_data else []
                }
                
                total_products = sum(len(data) for data in platforms_data.values())
                
                # 数据质量评估
                quality_score = min(100, (total_products / 80) * 100)
                
                return {
                    'aggregated_data': json.dumps(platforms_data),
                    'total_products': total_products,
                    'quality_score': quality_score,
                    'platforms_count': len([p for p in platforms_data.values() if p])
                }
          outputs:
            aggregated_data:
              type: string
            total_products:
              type: number
            quality_score:
              type: number
            platforms_count:
              type: number
        upstream: [crawl_douyin, crawl_taobao, crawl_amazon, crawl_pdd]

      # ========== 5. 智能产品分析节点 ==========
      - id: product_analyzer
        type: llm
        data:
          title: 🤖 AI产品深度分析
          model:
            provider: langgenius/openai/openai
            name: gpt-4o
            mode: chat
            completion_params:
              temperature: 0.2
              max_tokens: 2048
              top_p: 0.9
          prompt_template:
            - role: system
              text: |
                你是资深电商选品专家和市场分析师，具有丰富的跨平台电商经验。
                
                请基于提供的多平台商品数据进行深度分析，输出结构化的JSON格式：
                
                {
                  "market_overview": {
                    "category_trend": "类目整体趋势分析",
                    "competition_level": "low/medium/high",
                    "market_saturation": "饱和度评估"
                  },
                  "top_products": [
                    {
                      "platform": "平台名称",
                      "title": "商品标题",
                      "price_range": "价格区间",
                      "key_features": ["核心卖点1", "核心卖点2"],
                      "pros": ["优势1", "优势2", "优势3"],
                      "cons": ["劣势1", "劣势2"],
                      "unmet_needs": ["未满足需求1", "未满足需求2"],
                      "opportunity_score": 85,
                      "differentiation_suggestion": "差异化建议"
                    }
                  ],
                  "market_insights": {
                    "price_trends": "价格趋势分析",
                    "consumer_preferences": "消费者偏好",
                    "emerging_opportunities": "新兴机会点",
                    "seasonal_factors": "季节性因素"
                  },
                  "actionable_recommendations": [
                    "具体可行的选品建议1",
                    "具体可行的选品建议2"
                  ]
                }
                
                要求：
                1. 分析要深入细致，基于真实数据
                2. 识别市场空白和机会点
                3. 提供具体可执行的建议
                4. 考虑跨平台差异和特点
            - role: user
              text: |
                商品类目：{{ category_processor.chinese_category }}
                平台数据总览：
                - 抓取产品总数：{{ data_aggregator.total_products }}
                - 数据质量评分：{{ data_aggregator.quality_score }}
                - 覆盖平台数：{{ data_aggregator.platforms_count }}
                
                详细商品数据：
                {{ data_aggregator.aggregated_data }}
                
                请进行全面的市场分析和选品建议。
        upstream: [data_aggregator]

      # ========== 6. 机会评分计算节点 ==========
      - id: opportunity_scorer
        type: tool
        data:
          title: 📈 机会评分计算
          tool_name: opportunity_score_calculator
          tool_provider: mcp
          parameters:
            analysis_data: "{{ product_analyzer.text }}"
            category: "{{ category_processor.chinese_category }}"
            market_factors:
              competition_weight: 0.3
              demand_weight: 0.25
              profit_margin_weight: 0.25
              trend_weight: 0.2
        upstream: [product_analyzer]

      # ========== 7. 报告生成节点 ==========
      - id: report_generator
        type: code
        data:
          title: 📝 智能报告生成
          code: |
            import json
            from datetime import datetime
            
            def main(analysis_result: str, score_data: str, category: str) -> dict:
                try:
                    analysis = json.loads(analysis_result)
                    scores = json.loads(score_data) if score_data else {}
                    
                    # 生成时间戳
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    
                    # 构建完整报告
                    report = {
                        "report_meta": {
                            "category": category,
                            "generated_at": timestamp,
                            "report_version": "v2.1",
                            "analysis_depth": "comprehensive"
                        },
                        "executive_summary": {
                            "overall_opportunity": scores.get('overall_score', 'N/A'),
                            "recommendation_level": scores.get('recommendation', 'medium'),
                            "key_insights": analysis.get('actionable_recommendations', [])
                        },
                        "detailed_analysis": analysis,
                        "scoring_breakdown": scores
                    }
                    
                    # 生成markdown格式报告
                    markdown_report = f"""
                    # 📊 {category} 类目选品分析报告
                    
                    **生成时间：** {timestamp}
                    **分析深度：** 综合分析
                    
                    ## 🎯 执行摘要
                    - **整体机会评分：** {scores.get('overall_score', 'N/A')}
                    - **推荐等级：** {scores.get('recommendation', 'medium')}
                    
                    ## 📈 市场概况
                    {analysis.get('market_overview', {}).get('category_trend', '暂无数据')}
                    
                    ## 🏆 推荐产品
                    """
                    
                    for product in analysis.get('top_products', [])[:5]:
                        markdown_report += f"""
                        ### {product.get('title', '产品')} ({product.get('platform', '未知平台')})
                        - **价格区间：** {product.get('price_range', 'N/A')}
                        - **机会评分：** {product.get('opportunity_score', 'N/A')}
                        - **核心优势：** {', '.join(product.get('pros', []))}
                        - **改进建议：** {product.get('differentiation_suggestion', 'N/A')}
                        """
                    
                    return {
                        'full_report': json.dumps(report, ensure_ascii=False, indent=2),
                        'markdown_report': markdown_report,
                        'summary': f"已完成{category}类目分析，发现{len(analysis.get('top_products', []))}个机会产品"
                    }
                    
                except Exception as e:
                    return {
                        'full_report': f"报告生成失败：{str(e)}",
                        'markdown_report': f"# 报告生成错误\n\n{str(e)}",
                        'summary': f"报告生成过程中发生错误：{str(e)}"
                    }
          outputs:
            full_report:
              type: string
            markdown_report:
              type: string
            summary:
              type: string
        upstream: [product_analyzer, opportunity_scorer]

      # ========== 8. 数据存储节点 ==========
      - id: data_storage
        type: tool
        data:
          title: 💾 分析结果存储
          tool_name: save_analysis_report
          tool_provider: mcp
          parameters:
            report_data: "{{ report_generator.full_report }}"
            category: "{{ category_processor.chinese_category }}"
            storage_format: "json"
            include_timestamp: true
            backup_enabled: true
        upstream: [report_generator]

      # ========== 9. 最终回复节点 ==========
      - id: final_answer
        type: answer
        data:
          title: ✅ 分析完成
          answer: |
            ## 🎉 选品分析完成！
            
            {{ report_generator.summary }}
            
            {{ report_generator.markdown_report }}
            
            ---
            
            ### 📋 后续建议
            1. 重点关注高评分产品的市场表现
            2. 持续监控价格变化和竞争动态  
            3. 考虑产品差异化改进方案
            4. 建议每周更新一次分析数据
            
            💡 **提示：** 您可以继续询问其他类目的分析，或者深入了解特定产品的详细信息。
        upstream: [data_storage]

    edges:
      # 边缘关系通过upstream数组隐式定义
      # start -> category_processor -> [crawl_douyin, crawl_taobao, crawl_amazon, crawl_pdd] 
      # -> data_aggregator -> product_analyzer -> opportunity_scorer -> report_generator 
      # -> data_storage -> final_answer 