app:
  description: 电商爆品选品工作流 - 修订版步骤1：添加搜索工具
  icon: 🛍️
  icon_background: '#FF6B35'
  mode: advanced-chat
  name: 电商爆品选品工作流-修订版步骤1
  use_icon_as_answer_icon: false

dependencies: []
kind: app
version: 0.3.0

workflow:
  conversation_variables: []
  environment_variables: []
  
  features:
    file_upload:
      enabled: false
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false

  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: tool
      id: start-source-search_tool-target
      source: start
      sourceHandle: source
      target: search_tool
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: search_tool-source-analysis-target
      source: search_tool
      sourceHandle: source
      target: analysis
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: analysis-source-final_answer-target
      source: analysis
      sourceHandle: source
      target: final_answer
      targetHandle: target
      type: custom

    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 商品类目
          max_length: 256
          options: []
          required: true
          type: text-input
          variable: category
      height: 116
      id: start
      position:
        x: -400
        y: 300
      positionAbsolute:
        x: -400
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        desc: ''
        provider_id: builtin
        provider_name: builtin
        provider_type: builtin
        selected: false
        title: 市场搜索
        tool_configurations: {}
        tool_label: HTTP Request
        tool_name: http_request
        tool_parameters:
          url:
            type: constant
            value: "https://httpbin.org/json"
          method:
            type: constant
            value: "GET"
        type: tool
      height: 148
      id: search_tool
      position:
        x: -100
        y: 300
      positionAbsolute:
        x: -100
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.3
            max_tokens: 1024
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        prompt_template:
        - role: system
          text: |
            你是一位资深的电商选品专家。请基于用户提供的商品类目和搜索结果，给出详细的市场分析和选品建议。
            
            请用以下格式回答：
            
            ## 📊 {{category}} 类目分析
            
            ### 🔍 搜索信息
            [基于搜索结果的信息摘要]
            
            ### 市场概况
            [简要描述该类目的市场现状]
            
            ### 热门趋势
            [列出3-5个当前热门趋势]
            
            ### 选品建议
            [提供3-5个具体的选品建议]
            
            ### 注意事项
            [提醒需要注意的要点]
        - role: user
          text: |
            请分析商品类目的市场情况和选品机会：
            - 商品类目：{{#start.category#}}
            - 搜索结果：{{#search_tool.body#}}
        selected: false
        title: 市场分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 96
      id: analysis
      position:
        x: 200
        y: 300
      positionAbsolute:
        x: 200
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        answer: |
          {{#analysis.text#}}
          
          ---
          
          ### 📋 处理信息
          - **商品类目**: {{#start.category#}}
          - **搜索状态**: ✅ 已完成
          - **数据来源**: HTTP搜索工具
          
          💡 **下一步**: 我们将添加更多平台的数据搜索功能！
        desc: ''
        selected: false
        title: 分析结果
        type: answer
        variables: []
      height: 185
      id: final_answer
      position:
        x: 500
        y: 300
      positionAbsolute:
        x: 500
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    viewport:
      x: 400
      y: -300
      zoom: 1.0 