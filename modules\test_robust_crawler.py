#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强鲁棒性的爬虫
验证无标签时的备用策略
"""

import os
import time

def test_robust_crawler():
    """测试增强鲁棒性的爬虫"""
    print("🛡️  测试增强鲁棒性的爬虫")
    print("=" * 60)
    print("🎯 增强的鲁棒性策略:")
    print("   ✅ 检测无标签情况")
    print("   ✅ 启用备用策略：直接滚动提取")
    print("   ✅ 更充分的滚动（50次）")
    print("   ✅ 更宽松的评价验证")
    print("   ✅ 基于内容的智能分类")
    print("   ✅ 标签+备用策略混合使用")
    print()
    
    try:
        from final_taobao_review_crawler import FinalTaobaoReviewCrawler
        
        if not os.path.exists("taobao_cookies.pkl"):
            print("❌ 请先运行登录程序")
            return
        
        # 测试参数
        test_cases = [
            {
                "keyword": "ai对话盒子",
                "description": "评价较少的新兴产品",
                "expected_tags": "少"
            },
            {
                "keyword": "蓝牙耳机",
                "description": "评价丰富的热门产品",
                "expected_tags": "多"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"🧪 测试用例 {i}: {test_case['description']}")
            print(f"   关键词: {test_case['keyword']}")
            print(f"   预期标签: {test_case['expected_tags']}")
            print()
            
            # 创建爬虫
            crawler = FinalTaobaoReviewCrawler(headless=True, debug=True)
            
            if not crawler.login_with_cookies():
                print("❌ 登录失败")
                continue
            
            # 开始测试
            start_time = time.time()
            
            products = crawler.get_products_with_reviews(
                keyword=test_case['keyword'],
                max_results=3,  # 减少商品数量，专注测试策略
                max_reviews_per_product=50
            )
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # 分析结果
            if products:
                total_reviews = 0
                total_good = 0
                total_bad = 0
                total_medium = 0
                
                for product in products:
                    good_count = len(product.good_reviews)
                    bad_count = len(product.bad_reviews)
                    medium_count = len(product.medium_reviews)
                    
                    total_reviews += good_count + bad_count + medium_count
                    total_good += good_count
                    total_bad += bad_count
                    total_medium += medium_count
                
                print(f"🧪 测试用例 {i} 结果:")
                print(f"   耗时: {total_time:.1f}秒")
                print(f"   商品数: {len(products)}")
                print(f"   评价总数: {total_reviews}")
                print(f"   好评: {total_good} 条")
                print(f"   差评: {total_bad} 条")
                print(f"   中评: {total_medium} 条")
                print(f"   平均每商品: {total_reviews/len(products):.1f}条")
                
                # 鲁棒性评估
                if total_reviews > 0:
                    print(f"   ✅ 鲁棒性测试通过！成功获取评价")
                    
                    if test_case['expected_tags'] == "少" and total_reviews >= 5:
                        print(f"   🎉 备用策略成功！在少标签情况下获取到评价")
                    elif test_case['expected_tags'] == "多" and total_reviews >= 20:
                        print(f"   🎉 标签策略成功！在多标签情况下获取到丰富评价")
                else:
                    print(f"   ⚠️  鲁棒性需要进一步优化")
                
                print()
                
            else:
                print(f"❌ 测试用例 {i} 未获取到商品")
                print()
            
            crawler.close()
            time.sleep(2)  # 测试间隔
        
        print("🛡️  鲁棒性测试完成！")
        
    except Exception as e:
        print(f"❌ 鲁棒性测试失败: {e}")

def test_backup_strategy_only():
    """专门测试备用策略"""
    print("🔄 专门测试备用策略")
    print("=" * 40)
    
    try:
        from final_taobao_review_crawler import FinalTaobaoReviewCrawler
        
        if not os.path.exists("taobao_cookies.pkl"):
            print("❌ 请先运行登录程序")
            return
        
        # 创建爬虫
        crawler = FinalTaobaoReviewCrawler(headless=False, debug=True)  # 非无头模式便于观察
        
        if not crawler.login_with_cookies():
            print("❌ 登录失败")
            return
        
        # 测试单个商品的备用策略
        test_url = "https://item.taobao.com/item.htm?id=878929912114"  # 从之前的结果中选择一个
        
        print(f"🔄 测试备用策略:")
        print(f"   URL: {test_url}")
        print(f"   策略: 直接滚动提取（模拟无标签情况）")
        print()
        
        # 访问商品页面
        crawler.driver.get(test_url)
        time.sleep(2)
        
        # 点击全部评价
        if crawler._click_all_reviews():
            print("✅ 点击'全部评价'成功")
            
            # 直接使用备用策略
            backup_reviews = crawler._extract_reviews_without_tags(30)
            
            if backup_reviews:
                print(f"🔄 备用策略结果:")
                print(f"   评价数量: {len(backup_reviews)}")
                
                # 显示评价示例
                for i, review in enumerate(backup_reviews[:3], 1):
                    print(f"   {i}. {review.review_text[:60]}...")
                    print(f"      分类: {review.review_type}")
                
                print(f"✅ 备用策略测试成功！")
            else:
                print(f"⚠️  备用策略未获取到评价")
        else:
            print("❌ 点击'全部评价'失败")
        
        input("\n按回车键关闭浏览器...")
        crawler.close()
        
    except Exception as e:
        print(f"❌ 备用策略测试失败: {e}")

def compare_strategies():
    """对比不同策略的效果"""
    print("📊 策略效果对比")
    print("=" * 40)
    
    print("策略演进历程:")
    print("┌─────────────────────┬──────────┬──────────┬──────────┐")
    print("│ 策略版本            │ 适用场景 │ 优势     │ 局限性   │")
    print("├─────────────────────┼──────────┼──────────┼──────────┤")
    print("│ 原版容器滚动        │ 通用     │ 稳定     │ 无差评   │")
    print("│ 基于标签分类        │ 有标签   │ 有差评   │ 依赖标签 │")
    print("│ 🛡️ 增强鲁棒性版本    │ 全场景   │ 自适应   │ 复杂度高 │")
    print("└─────────────────────┴──────────┴──────────┴──────────┘")
    print()
    
    print("🛡️ 增强鲁棒性策略特点:")
    print("   1. 智能检测标签存在情况")
    print("   2. 有标签时使用标签策略")
    print("   3. 无标签时自动切换备用策略")
    print("   4. 标签策略效果不佳时补充备用策略")
    print("   5. 基于内容的智能评价分类")
    print()
    
    print("🎯 解决的问题:")
    print("   ✅ AI对话盒子等新兴产品评价少、无标签")
    print("   ✅ 不同商品标签数量差异大")
    print("   ✅ 单一策略适应性不足")
    print("   ✅ 提高整体成功率和数据获取量")

def main():
    """主函数"""
    print("🛡️  增强鲁棒性爬虫测试")
    print("=" * 60)
    print("🎯 解决的核心问题:")
    print("   在没有评价标签时，爬虫会失败")
    print("   AI对话盒子等新兴产品评价较少")
    print("   需要自适应的备用策略")
    print()
    
    print("选择测试模式:")
    print("1. 完整鲁棒性测试（推荐）")
    print("2. 专门测试备用策略")
    print("3. 策略效果对比")
    print("4. 退出")
    
    choice = input("请选择 (1-4): ")
    
    if choice == "1":
        test_robust_crawler()
    elif choice == "2":
        test_backup_strategy_only()
    elif choice == "3":
        compare_strategies()
    elif choice == "4":
        print("👋 再见！")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
