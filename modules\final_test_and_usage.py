#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版淘宝评价爬虫测试和使用脚本
整合所有成功的优化策略
"""

import os
import time

def test_final_crawler():
    """测试最终版爬虫"""
    print("🎉 最终版淘宝评价爬虫测试")
    print("=" * 60)
    print("🏆 集成的优化策略:")
    print("   ✅ 基于标签的评价分类提取")
    print("   ✅ 容器内滚动策略（30次滚动）")
    print("   ✅ 真实评价验证和过滤")
    print("   ✅ 差评获取解决方案")
    print("   ✅ 智能去重和分类")
    print()
    
    try:
        from final_taobao_review_crawler import FinalTaobaoReviewCrawler
        
        if not os.path.exists("taobao_cookies.pkl"):
            print("❌ 请先运行登录程序")
            return
        
        # 用户输入
        keyword = input("请输入搜索关键词 (默认: 蓝牙耳机): ").strip() or "蓝牙耳机"
        
        try:
            max_products = int(input("请输入商品数量 (默认: 5): ").strip() or "5")
        except:
            max_products = 5
        
        try:
            max_reviews = int(input("请输入每商品评价数 (默认: 60): ").strip() or "60")
        except:
            max_reviews = 60
        
        print(f"\n🎯 开始最终版测试:")
        print(f"   关键词: {keyword}")
        print(f"   商品数: {max_products}")
        print(f"   评价数: {max_reviews}/商品")
        print(f"   预期总评价: {max_products * max_reviews}")
        print()
        
        # 创建爬虫
        crawler = FinalTaobaoReviewCrawler(headless=True, debug=True)
        
        if not crawler.login_with_cookies():
            print("❌ 登录失败")
            return
        
        # 开始计时
        start_time = time.time()
        
        # 获取商品和评价
        products = crawler.get_products_with_reviews(
            keyword=keyword,
            max_results=max_products,
            max_reviews_per_product=max_reviews
        )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 统计结果
        if products:
            total_reviews = 0
            total_good = 0
            total_bad = 0
            total_medium = 0
            
            print(f"\n🎉 最终版测试结果:")
            print("=" * 50)
            
            for i, product in enumerate(products, 1):
                good_count = len(product.good_reviews)
                bad_count = len(product.bad_reviews)
                medium_count = len(product.medium_reviews)
                product_total = good_count + bad_count + medium_count
                
                total_reviews += product_total
                total_good += good_count
                total_bad += bad_count
                total_medium += medium_count
                
                print(f"📦 商品{i}: {product.title[:50]}...")
                print(f"   价格: {product.price} | 销量: {product.sales}")
                print(f"   评价: {product_total}条 (好评{good_count} 差评{bad_count} 中评{medium_count})")
                
                if product.bad_reviews:
                    print(f"   差评示例: {product.bad_reviews[0].review_text[:60]}...")
                print()
            
            # 总体统计
            print(f"📊 总体统计:")
            print(f"   总耗时: {total_time:.1f}秒")
            print(f"   商品数量: {len(products)}")
            print(f"   评价总数: {total_reviews}")
            print(f"   好评数量: {total_good} ({total_good/total_reviews*100:.1f}%)")
            print(f"   差评数量: {total_bad} ({total_bad/total_reviews*100:.1f}%)")
            print(f"   中评数量: {total_medium} ({total_medium/total_reviews*100:.1f}%)")
            print(f"   平均每商品: {total_time/len(products):.1f}秒")
            print(f"   提取速度: {total_reviews/total_time:.1f}条/秒")
            
            # 效果评估
            print(f"\n🎯 效果评估:")
            if total_bad >= 10:
                print(f"   🎉 差评获取优秀！获得 {total_bad} 条差评")
            elif total_bad >= 5:
                print(f"   ✅ 差评获取良好！获得 {total_bad} 条差评")
            elif total_bad >= 2:
                print(f"   ⚠️  差评获取一般，获得 {total_bad} 条差评")
            else:
                print(f"   ❌ 差评获取需要改进，仅获得 {total_bad} 条差评")
            
            if total_reviews >= max_products * max_reviews * 0.6:
                print(f"   ✅ 评价数量达标！完成率 {total_reviews/(max_products * max_reviews)*100:.1f}%")
            else:
                print(f"   ⚠️  评价数量偏少，完成率 {total_reviews/(max_products * max_reviews)*100:.1f}%")
            
            # 保存结果
            filepath = crawler.save_results(products, keyword)
            
            print(f"\n💾 结果已保存到: {filepath}")
            
        else:
            print("❌ 未获取到商品")
        
        crawler.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def quick_demo():
    """快速演示"""
    print("⚡ 快速演示最终版爬虫")
    print("=" * 40)
    
    try:
        from final_taobao_review_crawler import FinalTaobaoReviewCrawler
        
        if not os.path.exists("taobao_cookies.pkl"):
            print("❌ 请先运行登录程序")
            return
        
        # 固定参数快速演示
        keyword = "蓝牙耳机"
        max_products = 2
        max_reviews = 30
        
        print(f"🎯 快速演示参数:")
        print(f"   关键词: {keyword}")
        print(f"   商品数: {max_products}")
        print(f"   评价数: {max_reviews}/商品")
        print()
        
        # 创建爬虫
        crawler = FinalTaobaoReviewCrawler(headless=True, debug=True)
        
        if not crawler.login_with_cookies():
            print("❌ 登录失败")
            return
        
        # 开始演示
        start_time = time.time()
        
        products = crawler.get_products_with_reviews(
            keyword=keyword,
            max_results=max_products,
            max_reviews_per_product=max_reviews
        )
        
        end_time = time.time()
        
        # 简要结果
        if products:
            total_reviews = sum(len(p.good_reviews) + len(p.bad_reviews) + len(p.medium_reviews) for p in products)
            total_bad = sum(len(p.bad_reviews) for p in products)
            
            print(f"\n⚡ 快速演示结果:")
            print(f"   耗时: {end_time - start_time:.1f}秒")
            print(f"   商品: {len(products)}个")
            print(f"   评价: {total_reviews}条")
            print(f"   差评: {total_bad}条")
            
            if total_bad > 0:
                print(f"   ✅ 差评获取成功！")
            else:
                print(f"   ⚠️  差评获取需要优化")
        
        crawler.close()
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def usage_guide():
    """使用指南"""
    print("📖 最终版淘宝评价爬虫使用指南")
    print("=" * 60)
    
    print("🎯 核心功能:")
    print("   1. 商品搜索和信息提取")
    print("   2. 基于标签的评价分类提取")
    print("   3. 差评获取解决方案")
    print("   4. 真实评价验证和过滤")
    print("   5. 结果保存和分析")
    print()
    
    print("🚀 使用方法:")
    print("   1. 确保已运行登录程序获得cookies")
    print("   2. 导入最终版爬虫类")
    print("   3. 创建爬虫实例")
    print("   4. 调用get_products_with_reviews方法")
    print("   5. 保存和分析结果")
    print()
    
    print("💻 代码示例:")
    print("""
from final_taobao_review_crawler import FinalTaobaoReviewCrawler

# 创建爬虫
crawler = FinalTaobaoReviewCrawler(headless=True, debug=True)

# 登录
if crawler.login_with_cookies():
    # 获取商品和评价
    products = crawler.get_products_with_reviews(
        keyword="蓝牙耳机",
        max_results=10,
        max_reviews_per_product=60
    )
    
    # 保存结果
    filepath = crawler.save_results(products, "蓝牙耳机")
    
    # 关闭爬虫
    crawler.close()
""")
    
    print("📊 输出结果:")
    print("   - JSON格式的详细数据文件")
    print("   - 包含商品信息和分类评价")
    print("   - 支持进一步数据分析")
    print()
    
    print("🎯 优化特点:")
    print("   - 差评获取率显著提升")
    print("   - 真实评价验证过滤")
    print("   - 容器内精准滚动")
    print("   - 智能标签分类")
    print("   - 高质量数据输出")

def main():
    """主函数"""
    print("🎉 最终版淘宝评价爬虫")
    print("=" * 60)
    print("🏆 重大突破总结:")
    print("   ✅ 解决了差评获取难题")
    print("   ✅ 实现了容器内精准滚动")
    print("   ✅ 提升了评价数量和质量")
    print("   ✅ 集成了所有成功策略")
    print()
    
    print("选择操作:")
    print("1. 完整功能测试")
    print("2. 快速演示")
    print("3. 使用指南")
    print("4. 退出")
    
    choice = input("请选择 (1-4): ")
    
    if choice == "1":
        test_final_crawler()
    elif choice == "2":
        quick_demo()
    elif choice == "3":
        usage_guide()
    elif choice == "4":
        print("👋 再见！")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
