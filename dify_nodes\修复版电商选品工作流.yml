app:
  description: 电商爆品选品工作流 - 修复版
  icon: 🛍️
  icon_background: '#FF6B35'
  mode: advanced-chat
  name: 电商爆品选品工作流
  use_icon_as_answer_icon: false

dependencies: []
kind: app
version: 0.3.0

workflow:
  conversation_variables: []
  environment_variables: []
  
  features:
    file_upload:
      enabled: false
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false

  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: code
      id: start-source-category_processor-target
      source: start
      sourceHandle: source
      target: category_processor
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: code
        targetType: tool
      id: category_processor-source-crawl_douyin-target
      source: category_processor
      sourceHandle: source
      target: crawl_douyin
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: code
        targetType: tool
      id: category_processor-source-crawl_taobao-target
      source: category_processor
      sourceHandle: source
      target: crawl_taobao
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: code
        targetType: tool
      id: category_processor-source-crawl_amazon-target
      source: category_processor
      sourceHandle: source
      target: crawl_amazon
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: code
        targetType: tool
      id: category_processor-source-crawl_pdd-target
      source: category_processor
      sourceHandle: source
      target: crawl_pdd
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: code
      id: crawl_douyin-source-data_aggregator-target
      source: crawl_douyin
      sourceHandle: source
      target: data_aggregator
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: code
      id: crawl_taobao-source-data_aggregator-target
      source: crawl_taobao
      sourceHandle: source
      target: data_aggregator
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: code
      id: crawl_amazon-source-data_aggregator-target
      source: crawl_amazon
      sourceHandle: source
      target: data_aggregator
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: code
      id: crawl_pdd-source-data_aggregator-target
      source: crawl_pdd
      sourceHandle: source
      target: data_aggregator
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: data_aggregator-source-product_analyzer-target
      source: data_aggregator
      sourceHandle: source
      target: product_analyzer
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: product_analyzer-source-opportunity_scorer-target
      source: product_analyzer
      sourceHandle: source
      target: opportunity_scorer
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: code
      id: opportunity_scorer-source-report_generator-target
      source: opportunity_scorer
      sourceHandle: source
      target: report_generator
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: code
        targetType: answer
      id: report_generator-source-final_answer-target
      source: report_generator
      sourceHandle: source
      target: final_answer
      targetHandle: target
      type: custom

    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 商品类目
          max_length: 256
          options: []
          required: true
          type: text-input
          variable: category
      height: 116
      id: start
      position:
        x: -634
        y: 720
      positionAbsolute:
        x: -634
        y: 720
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        code: |
          def main(category: str) -> dict:
              # 简单的类目处理
              cleaned_category = category.strip()
              
              # 英文翻译映射
              category_mapping = {
                  '家居服': 'home clothes',
                  '数码配件': 'digital accessories', 
                  '美妆护肤': 'beauty skincare',
                  '运动健身': 'sports fitness'
              }
              
              english_category = category_mapping.get(cleaned_category, cleaned_category)
              
              return {
                  'chinese_category': cleaned_category,
                  'english_category': english_category,
                  'search_ready': True
              }
        desc: ''
        outputs:
          chinese_category:
            type: string
          english_category: 
            type: string
          search_ready:
            type: boolean
        selected: false
        title: 类目处理
        type: code
      height: 168
      id: category_processor
      position:
        x: -334
        y: 720
      positionAbsolute:
        x: -334
        y: 720
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        desc: ''
        selected: false
        title: 抖音数据抓取
        tool_label: 抖音爬虫
        tool_name: douyin_crawler
        tool_parameters:
          keyword:
            type: mixed
            value: '{{#category_processor.chinese_category#}}'
        tool_provider_id: builtin
        tool_provider_type: builtin
        type: tool
      height: 148
      id: crawl_douyin
      position:
        x: -34
        y: 620
      positionAbsolute:
        x: -34
        y: 620
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        desc: ''
        selected: false
        title: 淘宝数据抓取
        tool_label: 淘宝爬虫
        tool_name: taobao_crawler
        tool_parameters:
          keyword:
            type: mixed
            value: '{{#category_processor.chinese_category#}}'
        tool_provider_id: builtin
        tool_provider_type: builtin
        type: tool
      height: 148
      id: crawl_taobao
      position:
        x: -34
        y: 720
      positionAbsolute:
        x: -34
        y: 720
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        desc: ''
        selected: false
        title: 亚马逊数据抓取
        tool_label: 亚马逊爬虫
        tool_name: amazon_crawler
        tool_parameters:
          keyword:
            type: mixed
            value: '{{#category_processor.english_category#}}'
        tool_provider_id: builtin
        tool_provider_type: builtin
        type: tool
      height: 148
      id: crawl_amazon
      position:
        x: -34
        y: 820
      positionAbsolute:
        x: -34
        y: 820
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        desc: ''
        selected: false
        title: 拼多多数据抓取
        tool_label: 拼多多爬虫
        tool_name: pdd_crawler
        tool_parameters:
          keyword:
            type: mixed
            value: '{{#category_processor.chinese_category#}}'
        tool_provider_id: builtin
        tool_provider_type: builtin
        type: tool
      height: 148
      id: crawl_pdd
      position:
        x: -34
        y: 920
      positionAbsolute:
        x: -34
        y: 920
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        code: |
          def main(douyin_data: str, taobao_data: str, amazon_data: str, pdd_data: str) -> dict:
              # 简单的数据聚合
              platforms_data = {}
              total_products = 0
              
              if douyin_data:
                  platforms_data['douyin'] = douyin_data
                  total_products += 20
              if taobao_data:
                  platforms_data['taobao'] = taobao_data
                  total_products += 20
              if amazon_data:
                  platforms_data['amazon'] = amazon_data
                  total_products += 20
              if pdd_data:
                  platforms_data['pdd'] = pdd_data
                  total_products += 20
              
              quality_score = (len(platforms_data) / 4) * 100
              
              return {
                  'aggregated_data': str(platforms_data),
                  'total_products': total_products,
                  'quality_score': quality_score
              }
        desc: ''
        outputs:
          aggregated_data:
            type: string
          total_products:
            type: number
          quality_score:
            type: number
        selected: false
        title: 数据聚合
        type: code
      height: 168
      id: data_aggregator
      position:
        x: 266
        y: 720
      positionAbsolute:
        x: 266
        y: 720
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.3
            max_tokens: 1024
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        prompt_template:
        - role: system
          text: |
            你是一位资深的电商选品专家。请基于提供的多平台商品数据进行分析。
            
            请提供以下内容：
            1. 市场概况分析
            2. 热门产品特点
            3. 价格趋势
            4. 选品建议
        - role: user
          text: |
            商品类目：{{#category_processor.chinese_category#}}
            数据质量评分：{{#data_aggregator.quality_score#}}
            产品总数：{{#data_aggregator.total_products#}}
            
            聚合数据：{{#data_aggregator.aggregated_data#}}
            
            请进行市场分析和选品建议。
        selected: false
        title: AI产品分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 96
      id: product_analyzer
      position:
        x: 566
        y: 720
      positionAbsolute:
        x: 566
        y: 720
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        desc: ''
        selected: false
        title: 机会评分计算
        tool_label: 评分计算器
        tool_name: score_calculator
        tool_parameters:
          analysis_data:
            type: mixed
            value: '{{#product_analyzer.text#}}'
        tool_provider_id: builtin
        tool_provider_type: builtin
        type: tool
      height: 148
      id: opportunity_scorer
      position:
        x: 866
        y: 720
      positionAbsolute:
        x: 866
        y: 720
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        code: |
          def main(analysis_result: str, score_data: str) -> dict:
              # 生成简单报告
              timestamp = "2024-01-01 12:00:00"
              
              report_summary = f"选品分析报告已于 {timestamp} 生成完成"
              
              full_report = f"""
              # 电商选品分析报告
              
              生成时间：{timestamp}
              
              ## AI分析结果
              {analysis_result}
              
              ## 评分结果
              {score_data if score_data else '评分计算中...'}
              
              ## 总结
              基于以上分析，我们为您提供了全面的选品建议。
              """
              
              return {
                  'full_report': full_report,
                  'summary': report_summary
              }
        desc: ''
        outputs:
          full_report:
            type: string
          summary:
            type: string
        selected: false
        title: 报告生成
        type: code
      height: 168
      id: report_generator
      position:
        x: 1166
        y: 720
      positionAbsolute:
        x: 1166
        y: 720
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        answer: |
          ## 🎉 电商选品分析完成！
          
          {{#report_generator.summary#}}
          
          {{#report_generator.full_report#}}
          
          ---
          
          💡 **提示**：如需更详细的分析或针对特定产品的深入研究，请继续提问。
        desc: ''
        selected: false
        title: 分析完成
        type: answer
        variables: []
      height: 185
      id: final_answer
      position:
        x: 1466
        y: 720
      positionAbsolute:
        x: 1466
        y: 720
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    viewport:
      x: 634
      y: -720
      zoom: 1.0 