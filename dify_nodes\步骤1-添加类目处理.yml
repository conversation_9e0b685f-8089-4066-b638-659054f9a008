app:
  description: 电商爆品选品工作流 - 步骤1：添加类目处理
  icon: 🛍️
  icon_background: '#FF6B35'
  mode: advanced-chat
  name: 电商爆品选品工作流-步骤1
  use_icon_as_answer_icon: false

dependencies: []
kind: app
version: 0.3.0

workflow:
  conversation_variables: []
  environment_variables: []
  
  features:
    file_upload:
      enabled: false
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false

  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: code
      id: start-source-category_processor-target
      source: start
      sourceHandle: source
      target: category_processor
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: code
        targetType: llm
      id: category_processor-source-simple_analysis-target
      source: category_processor
      sourceHandle: source
      target: simple_analysis
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: simple_analysis-source-final_answer-target
      source: simple_analysis
      sourceHandle: source
      target: final_answer
      targetHandle: target
      type: custom

    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 商品类目
          max_length: 256
          options: []
          required: true
          type: text-input
          variable: category
      height: 116
      id: start
      position:
        x: -400
        y: 300
      positionAbsolute:
        x: -400
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        code: |
          def main(category: str) -> dict:
              # 清理和标准化类目名称
              cleaned_category = category.strip()
              
              # 英文翻译映射
              category_mapping = {
                  '家居服': 'home clothes',
                  '数码配件': 'digital accessories', 
                  '美妆护肤': 'beauty skincare',
                  '运动健身': 'sports fitness',
                  '母婴用品': 'baby products',
                  '厨房用品': 'kitchen supplies',
                  '服装鞋帽': 'clothing shoes',
                  '汽车用品': 'car accessories'
              }
              
              english_category = category_mapping.get(cleaned_category, cleaned_category)
              
              return {
                  'chinese_category': cleaned_category,
                  'english_category': english_category,
                  'search_ready': True
              }
        desc: ''
        outputs:
          chinese_category:
            type: string
          english_category: 
            type: string
          search_ready:
            type: boolean
        selected: false
        title: 类目处理
        type: code
      height: 168
      id: category_processor
      position:
        x: -100
        y: 300
      positionAbsolute:
        x: -100
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.3
            max_tokens: 1024
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        prompt_template:
        - role: system
          text: |
            你是一位资深的电商选品专家。请基于用户提供的商品类目，给出简要的市场分析和选品建议。
            
            请用以下格式回答：
            
            ## 📊 {{chinese_category}} 类目分析
            
            ### 市场概况
            [简要描述该类目的市场现状]
            
            ### 热门趋势
            [列出3-5个当前热门趋势]
            
            ### 选品建议
            [提供3-5个具体的选品建议]
            
            ### 注意事项
            [提醒需要注意的要点]
        - role: user
          text: |
            请分析商品类目的市场情况和选品机会：
            - 中文类目：{{#category_processor.chinese_category#}}
            - 英文类目：{{#category_processor.english_category#}}
            - 处理状态：{{#category_processor.search_ready#}}
        selected: false
        title: 类目分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 96
      id: simple_analysis
      position:
        x: 200
        y: 300
      positionAbsolute:
        x: 200
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    - data:
        answer: |
          {{#simple_analysis.text#}}
          
          ---
          
          ### 📋 处理信息
          - **中文类目**: {{#category_processor.chinese_category#}}
          - **英文类目**: {{#category_processor.english_category#}}
          - **处理状态**: ✅ 已完成
          
          💡 **下一步**: 我们将逐步添加多平台数据抓取功能！
        desc: ''
        selected: false
        title: 分析结果
        type: answer
        variables: []
      height: 185
      id: final_answer
      position:
        x: 500
        y: 300
      positionAbsolute:
        x: 500
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    viewport:
      x: 400
      y: -300
      zoom: 1.0 