@echo off
echo ========================================
echo 启动淘宝爬虫HTTP MCP服务器
echo ========================================
echo.

REM 激活conda环境
echo 激活conda环境: torch
call conda activate torch
if errorlevel 1 (
    echo 错误: 无法激活conda环境 torch
    echo 请确保已安装conda并创建了torch环境
    pause
    exit /b 1
)

REM 检查Python环境
echo 检查Python环境...
python -c "import fastmcp; print('FastMCP库已安装')" 2>nul
if errorlevel 1 (
    echo 错误: FastMCP库未安装
    echo 请运行: pip install fastmcp
    pause
    exit /b 1
)

REM 检查爬虫文件
if not exist "final_taobao_review_crawler.py" (
    echo 错误: 找不到爬虫文件 final_taobao_review_crawler.py
    echo 请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

REM 启动HTTP MCP服务器
echo.
echo 启动HTTP MCP服务器...
echo 服务器URL: http://*************:8001/mcp/
echo 绑定地址: ************* (局域网地址)
echo 按 Ctrl+C 停止服务器
echo.

python taobao_mcp_http_server.py

echo.
echo 服务器已停止
pause
