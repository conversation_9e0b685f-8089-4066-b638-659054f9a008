# 解决Dify MCP 503错误指南

## 🚨 问题描述

在Dify中创建MCP服务器时出现错误：
```
Failed to re-connect MCP server: code=503 message="Server error '503 Service Unavailable' for url 'http://0.0.0.0:8000/mcp/'"
```

## 🔍 问题分析

### 503错误的常见原因
1. **服务器未启动**或已崩溃
2. **端口冲突**或被占用
3. **网络代理问题**（如系统代理设置）
4. **主机绑定问题**（0.0.0.0 vs 127.0.0.1）
5. **防火墙阻止**连接

## ✅ 解决方案

### 步骤1: 检查服务器状态
```bash
# 检查服务器是否运行
curl http://127.0.0.1:8001/mcp/

# 应该返回MCP协议响应，而不是连接错误
```

### 步骤2: 重新启动服务器
```bash
# 停止现有服务器（如果有）
# 按 Ctrl+C 停止

# 启动新服务器
cd modules
python taobao_mcp_http_server.py
```

### 步骤3: 验证服务器配置
确认服务器显示以下信息：
```
🚀 启动淘宝爬虫MCP HTTP服务器
==================================================
📡 协议: Model Context Protocol (HTTP)
🌐 传输: HTTP (Dify兼容)
🔧 端口: 8001
🌐 服务器URL: http://127.0.0.1:8001/mcp/
📋 健康检查: http://127.0.0.1:8001/health
```

### 步骤4: 在Dify中使用正确URL
在Dify中添加MCP服务器时使用：
- **Server URL**: `http://127.0.0.1:8001/mcp/`
- **不要使用**: `http://0.0.0.0:8000/mcp/` 或 `http://localhost:8000/mcp/`

## 🛠️ 详细故障排除

### 检查1: 端口占用
```bash
# Windows
netstat -ano | findstr :8001

# Linux/Mac
lsof -i :8001
```

### 检查2: 代理设置
如果系统使用HTTP代理，可能需要：
1. 临时禁用代理
2. 或在代理中添加localhost例外

### 检查3: 防火墙
确保防火墙允许8001端口的本地连接

### 检查4: Python环境
```bash
# 确认在正确的conda环境中
conda activate torch

# 检查FastMCP安装
python -c "from fastmcp import FastMCP; print('FastMCP OK')"
```

## 🎯 最佳实践

### 1. 服务器配置
- 使用 `127.0.0.1` 而不是 `0.0.0.0`
- 选择未被占用的端口（如8001）
- 确保服务器持续运行

### 2. Dify配置
- 使用完整的URL：`http://127.0.0.1:8001/mcp/`
- 服务器ID保持一致：`taobao-crawler-http`
- 等待服务器完全启动后再添加

### 3. 网络环境
- 检查代理设置
- 确认防火墙配置
- 使用本地回环地址

## 🔄 完整重启流程

如果问题持续存在，按以下步骤完全重启：

### 1. 停止所有相关进程
```bash
# 停止MCP服务器
# 按 Ctrl+C

# 检查是否有残留进程
tasklist | findstr python  # Windows
ps aux | grep python       # Linux/Mac
```

### 2. 重新启动服务器
```bash
cd modules
conda activate torch
python taobao_mcp_http_server.py
```

### 3. 验证连接
```bash
curl -H "Accept: text/event-stream" http://127.0.0.1:8001/mcp/
```

### 4. 在Dify中重新添加
- 删除旧的MCP服务器配置（如果存在）
- 添加新的服务器：`http://127.0.0.1:8001/mcp/`

## 📞 仍然有问题？

如果按照以上步骤仍然出现503错误：

1. **检查日志**: 查看服务器终端输出的错误信息
2. **网络诊断**: 使用 `telnet 127.0.0.1 8001` 测试连接
3. **环境隔离**: 尝试在不同的网络环境中测试
4. **版本检查**: 确认FastMCP和MCP库版本兼容

## ✅ 成功标志

当一切正常时，你应该看到：
- ✅ 服务器成功启动并显示正确的URL
- ✅ curl测试返回MCP协议响应
- ✅ Dify成功连接并发现工具
- ✅ 工具调用正常工作

现在你应该能够成功在Dify中使用淘宝爬虫MCP服务器了！🎉
