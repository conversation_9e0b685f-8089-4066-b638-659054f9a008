app:
  description: 🛍️ 电商爆品选品专家 - 基于MCP协议的淘宝智能分析系统，提供专业的选品分析和需求洞察
  icon: 🛍️
  icon_background: '#FF6B35'
  mode: advanced-chat
  name: 电商爆品选品专家-MCP优化版
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/gemini:0.2.7@b8a04c0155eb3b9d43ed1199b4387e7f67ef75ad63fcec466eab31a726e2c3a0
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: '🎯 欢迎使用电商爆品选品专家！我是您的专业选品顾问，基于MCP协议标准化淘宝数据，为您提供精准的选品分析。


      🎉 **MCP协议驱动 - 真实数据分析**：

      ✅ Model Context Protocol标准接口 - 数据质量保证

      ✅ 差评获取率33.3% - 深度洞察产品问题

      ✅ 94.4%真实评价率 - 高质量数据分析

      ✅ 智能标签分类 - 精准需求识别


      🗣️ **自然输入方式**：

      您可以用自然语言描述您的需求，比如：

      • "我想分析蓝牙耳机的市场机会"

      • "帮我看看运动健身类产品怎么样"

      • "手机壳好卖吗？"

      • "分析一下充电器的选品机会"


      我会基于真实的淘宝数据，为您生成专业的选品分析报告！

      '
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions:
    - 我想分析蓝牙耳机的市场机会
    - 帮我看看运动健身类产品怎么样
    - 手机壳好卖吗？
    - 分析一下充电器的选品机会
    - 美妆护肤产品的市场潜力如何？
    suggested_questions_after_answer:
      enabled: true
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: start-source-keyword_extractor-target
      source: start
      sourceHandle: source
      target: keyword_extractor
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: keyword_extractor-source-mcp_taobao_crawler-target
      source: keyword_extractor
      sourceHandle: source
      target: mcp_taobao_crawler
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: mcp_taobao_crawler-source-structured_analysis-target
      source: mcp_taobao_crawler
      sourceHandle: source
      target: structured_analysis
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: llm
        targetType: assigner
      id: structured_analysis-source-score_calculator-target
      source: structured_analysis
      sourceHandle: source
      target: score_calculator
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: assigner
        targetType: llm
      id: score_calculator-source-format_output-target
      source: score_calculator
      sourceHandle: source
      target: format_output
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: format_output-source-final_answer-target
      source: format_output
      sourceHandle: source
      target: final_answer
      targetHandle: target
      type: custom
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 您想分析什么商品？
          max_length: 512
          options: []
          required: true
          type: text-input
          variable: user_input
      height: 90
      id: start
      position:
        x: -800
        y: 300
      positionAbsolute:
        x: -800
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: 智能解析用户输入，提取商品关键词和分类信息
        model:
          completion_params:
            temperature: 0.1
          mode: chat
          name: gemini-2.5-flash
          provider: langgenius/gemini/google
        prompt_template:
        - id: 5ab4d6d8-c026-4d68-a9d1-80a9305cc5e9
          role: system
          text: '你是一个专业的商品关键词提取专家。你的任务是从用户的自然语言输入中提取出准确的商品关键词。


            🎯 **提取规则：**

            1. 提取核心商品名称，去除修饰词

            2. 保持2-6个字的长度

            3. 使用标准的商品类目名称

            4. 避免过于宽泛或过于具体


            📝 **输出格式：**

            只输出提取的关键词，不要任何解释或额外文字。


            🌰 **示例：**

            - 输入："我想分析蓝牙耳机的市场机会" → 输出："蓝牙耳机"

            - 输入："帮我看看运动健身类产品怎么样" → 输出："运动健身"

            - 输入："手机壳好卖吗" → 输出："手机壳"

            - 输入："分析一下充电器的选品机会" → 输出："充电器"

            - 输入："美妆护肤产品的市场潜力" → 输出："美妆护肤"

            '
        - id: 396e6c99-c616-4300-83aa-86708ec5eabf
          role: user
          text: '请从以下用户输入中提取商品关键词：


            用户输入：{{#start.user_input#}}


            请只输出提取的关键词，不要任何其他内容。

            '
        selected: false
        structured_output:
          schema:
            properties:
              category:
                description: 商品分类
                type: string
              confidence:
                description: 提取置信度
                maximum: 1
                minimum: 0
                type: number
              keyword:
                description: 提取的商品关键词
                type: string
            required:
            - keyword
            - category
            - confidence
            type: object
        structured_output_enabled: true
        title: 🧠 智能关键词提取器
        type: llm
        variables: []
        vision:
          enabled: false
      height: 134
      id: keyword_extractor
      position:
        x: -500
        y: 300
      positionAbsolute:
        x: -500
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
