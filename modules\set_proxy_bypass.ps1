# PowerShell脚本：设置代理绕过localhost
Write-Host "设置代理绕过localhost" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

# 设置环境变量
$env:no_proxy = "127.0.0.1,localhost,127.0.0.1:8001,localhost:8001"
$env:NO_PROXY = "127.0.0.1,localhost,127.0.0.1:8001,localhost:8001"

Write-Host "已设置代理绕过:" -ForegroundColor Yellow
Write-Host "no_proxy = $env:no_proxy" -ForegroundColor Cyan
Write-Host "NO_PROXY = $env:NO_PROXY" -ForegroundColor Cyan

Write-Host ""
Write-Host "现在可以启动Dify或其他需要访问localhost的应用" -ForegroundColor Green

# 可选：启动一个新的PowerShell会话，继承这些环境变量
Write-Host "是否要启动新的PowerShell会话？(y/n): " -NoNewline -ForegroundColor Yellow
$response = Read-Host
if ($response -eq 'y' -or $response -eq 'Y') {
    Start-Process powershell -ArgumentList "-NoExit", "-Command", "Write-Host '代理绕过已设置，可以在此会话中启动Dify' -ForegroundColor Green"
}
