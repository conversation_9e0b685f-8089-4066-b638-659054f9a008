#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Dify工作流中的变量赋值器操作类型问题
将 'assign' 操作改为 'set' 以兼容新版Dify
"""

import yaml
import json
import os

def fix_variable_assigner_operations(file_path):
    """修复变量赋值器操作类型"""
    print(f"🔧 修复工作流文件: {file_path}")
    
    try:
        # 读取YAML文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换 operation: assign 为 operation: set
        original_content = content
        content = content.replace('operation: assign', 'operation: set')
        
        # 统计替换次数
        assign_count = original_content.count('operation: assign')
        set_count = content.count('operation: set')
        
        print(f"   📊 发现 {assign_count} 个 'assign' 操作")
        print(f"   ✅ 替换为 {set_count} 个 'set' 操作")
        
        if assign_count > 0:
            # 写回文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"   💾 文件已更新")
            return True
        else:
            print(f"   ℹ️  无需修复")
            return False
            
    except Exception as e:
        print(f"   ❌ 修复失败: {e}")
        return False

def validate_yaml_syntax(file_path):
    """验证YAML语法"""
    print(f"🔍 验证YAML语法: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            yaml.safe_load(f)
        print("   ✅ YAML语法正确")
        return True
    except yaml.YAMLError as e:
        print(f"   ❌ YAML语法错误: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 文件读取错误: {e}")
        return False

def create_backup(file_path):
    """创建备份文件"""
    backup_path = file_path + '.backup'
    try:
        with open(file_path, 'r', encoding='utf-8') as src:
            with open(backup_path, 'w', encoding='utf-8') as dst:
                dst.write(src.read())
        print(f"   💾 备份已创建: {backup_path}")
        return True
    except Exception as e:
        print(f"   ❌ 备份创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Dify工作流修复工具")
    print("=" * 50)
    
    # 要修复的文件列表
    files_to_fix = [
        "../dify_nodes/电商爆品选品专家-集成版.yml"
    ]
    
    results = []
    
    for file_path in files_to_fix:
        print(f"\n📁 处理文件: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"   ❌ 文件不存在")
            results.append((file_path, False, "文件不存在"))
            continue
        
        # 创建备份
        backup_success = create_backup(file_path)
        if not backup_success:
            print(f"   ⚠️  备份失败，继续处理...")
        
        # 验证原始语法
        if not validate_yaml_syntax(file_path):
            results.append((file_path, False, "原始YAML语法错误"))
            continue
        
        # 修复操作类型
        fix_success = fix_variable_assigner_operations(file_path)
        
        # 验证修复后的语法
        if fix_success:
            if validate_yaml_syntax(file_path):
                results.append((file_path, True, "修复成功"))
            else:
                results.append((file_path, False, "修复后YAML语法错误"))
        else:
            results.append((file_path, True, "无需修复"))
    
    # 输出结果汇总
    print("\n" + "=" * 50)
    print("📋 修复结果汇总")
    print("=" * 50)
    
    success_count = 0
    for file_path, success, message in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{status} {os.path.basename(file_path)}: {message}")
        if success:
            success_count += 1
    
    print(f"\n📊 总计: {success_count}/{len(results)} 个文件处理成功")
    
    if success_count == len(results):
        print("\n🎉 所有文件修复完成！")
        print("💡 现在可以在Dify中导入修复后的工作流文件")
    else:
        print("\n⚠️  部分文件修复失败，请检查错误信息")
    
    print("\n📁 可用的工作流文件:")
    print("   1. 电商爆品选品专家-集成版.yml (修复后)")
    print("   2. 电商爆品选品专家-简化版.yml (新版本，无评分计算器)")
    print("\n💡 推荐使用简化版，更稳定且兼容性更好")

if __name__ == "__main__":
    main()
