app:
  description: 电商爆品选品工作流 - 步骤4：变量计算处理
  icon: 🛍️
  icon_background: '#FF6B35'
  mode: advanced-chat
  name: 电商爆品选品工作流-步骤4
  use_icon_as_answer_icon: false

dependencies: []
kind: app
version: 0.3.0

workflow:
  conversation_variables: []
  environment_variables: []
  
  features:
    file_upload:
      enabled: false
    opening_statement: ''
    retriever_resource:
      enabled: false
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false

  graph:
    edges:
    # Start 到四个搜索工具的连接
    - data:
        isInLoop: false
        sourceType: start
        targetType: tool
      id: start-source-douyin_search-target
      source: start
      sourceHandle: source
      target: douyin_search
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: start
        targetType: tool
      id: start-source-taobao_search-target
      source: start
      sourceHandle: source
      target: taobao_search
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: start
        targetType: tool
      id: start-source-amazon_search-target
      source: start
      sourceHandle: source
      target: amazon_search
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: start
        targetType: tool
      id: start-source-pdd_search-target
      source: start
      sourceHandle: source
      target: pdd_search
      targetHandle: target
      type: custom
    
    # 四个搜索工具到结构化分析的连接
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: douyin_search-source-structured_analysis-target
      source: douyin_search
      sourceHandle: source
      target: structured_analysis
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: taobao_search-source-structured_analysis-target
      source: taobao_search
      sourceHandle: source
      target: structured_analysis
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: amazon_search-source-structured_analysis-target
      source: amazon_search
      sourceHandle: source
      target: structured_analysis
      targetHandle: target
      type: custom
    - data:
        isInLoop: false
        sourceType: tool
        targetType: llm
      id: pdd_search-source-structured_analysis-target
      source: pdd_search
      sourceHandle: source
      target: structured_analysis
      targetHandle: target
      type: custom
    
    # 结构化分析到变量计算的连接
    - data:
        isInLoop: false
        sourceType: llm
        targetType: assigner
      id: structured_analysis-source-score_calculator-target
      source: structured_analysis
      sourceHandle: source
      target: score_calculator
      targetHandle: target
      type: custom
    
    # 变量计算到格式化输出的连接
    - data:
        isInLoop: false
        sourceType: assigner
        targetType: llm
      id: score_calculator-source-format_output-target
      source: score_calculator
      sourceHandle: source
      target: format_output
      targetHandle: target
      type: custom
    
    # 格式化输出到Answer的连接
    - data:
        isInLoop: false
        sourceType: llm
        targetType: answer
      id: format_output-source-final_answer-target
      source: format_output
      sourceHandle: source
      target: final_answer
      targetHandle: target
      type: custom

    nodes:
    # 开始节点
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables:
        - label: 商品类目
          max_length: 256
          options: []
          required: true
          type: text-input
          variable: category
      height: 116
      id: start
      position:
        x: -700
        y: 300
      positionAbsolute:
        x: -700
        y: 300
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 抖音搜索工具
    - data:
        desc: ''
        provider_id: builtin
        provider_name: builtin
        provider_type: builtin
        selected: false
        title: 抖音搜索
        tool_configurations: {}
        tool_label: HTTP Request
        tool_name: http_request
        tool_parameters:
          url:
            type: constant
            value: "https://httpbin.org/json"
          method:
            type: constant
            value: "GET"
          headers:
            type: constant
            value: "Content-Type: application/json\nPlatform: Douyin"
        type: tool
      height: 148
      id: douyin_search
      position:
        x: -400
        y: 80
      positionAbsolute:
        x: -400
        y: 80
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 淘宝搜索工具
    - data:
        desc: ''
        provider_id: builtin
        provider_name: builtin
        provider_type: builtin
        selected: false
        title: 淘宝搜索
        tool_configurations: {}
        tool_label: HTTP Request
        tool_name: http_request
        tool_parameters:
          url:
            type: constant
            value: "https://httpbin.org/json"
          method:
            type: constant
            value: "GET"
          headers:
            type: constant
            value: "Content-Type: application/json\nPlatform: Taobao"
        type: tool
      height: 148
      id: taobao_search
      position:
        x: -400
        y: 250
      positionAbsolute:
        x: -400
        y: 250
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 亚马逊搜索工具
    - data:
        desc: ''
        provider_id: builtin
        provider_name: builtin
        provider_type: builtin
        selected: false
        title: 亚马逊搜索
        tool_configurations: {}
        tool_label: HTTP Request
        tool_name: http_request
        tool_parameters:
          url:
            type: constant
            value: "https://httpbin.org/json"
          method:
            type: constant
            value: "GET"
          headers:
            type: constant
            value: "Content-Type: application/json\nPlatform: Amazon"
        type: tool
      height: 148
      id: amazon_search
      position:
        x: -400
        y: 420
      positionAbsolute:
        x: -400
        y: 420
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 拼多多搜索工具
    - data:
        desc: ''
        provider_id: builtin
        provider_name: builtin
        provider_type: builtin
        selected: false
        title: 拼多多搜索
        tool_configurations: {}
        tool_label: HTTP Request
        tool_name: http_request
        tool_parameters:
          url:
            type: constant
            value: "https://httpbin.org/json"
          method:
            type: constant
            value: "GET"
          headers:
            type: constant
            value: "Content-Type: application/json\nPlatform: Pinduoduo"
        type: tool
      height: 148
      id: pdd_search
      position:
        x: -400
        y: 590
      positionAbsolute:
        x: -400
        y: 590
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 结构化分析节点（带JSON输出）
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.2
            max_tokens: 2048
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        prompt_template:
        - role: system
          text: |
            你是一位电商选品专家。请基于多平台搜索数据，输出标准化的JSON分析结果。
            
            严格按照以下JSON schema输出结果：
            
            {
              "category": "商品类目",
              "platforms": {
                "douyin": {
                  "market_trend": "市场趋势描述",
                  "hot_products": ["热门产品1", "热门产品2"],
                  "opportunity_score": 0-100的评分
                },
                "taobao": {
                  "market_trend": "市场趋势描述", 
                  "hot_products": ["热门产品1", "热门产品2"],
                  "opportunity_score": 0-100的评分
                },
                "amazon": {
                  "market_trend": "市场趋势描述",
                  "hot_products": ["热门产品1", "热门产品2"], 
                  "opportunity_score": 0-100的评分
                },
                "pinduoduo": {
                  "market_trend": "市场趋势描述",
                  "hot_products": ["热门产品1", "热门产品2"],
                  "opportunity_score": 0-100的评分
                }
              },
              "overall_analysis": {
                "market_size": "大/中/小",
                "competition_level": "高/中/低", 
                "profit_potential": "高/中/低",
                "recommended_products": ["推荐产品1", "推荐产品2", "推荐产品3"],
                "market_gaps": ["市场空白1", "市场空白2"],
                "risk_factors": ["风险因素1", "风险因素2"]
              },
              "scores": {
                "overall_opportunity": 0-100的总体机会评分,
                "market_potential": 0-100的市场潜力评分,
                "competition_risk": 0-100的竞争风险评分
              }
            }
        - role: user
          text: |
            基于以下多平台数据进行结构化分析：
            
            商品类目: {{#start.category#}}
            抖音数据: {{#douyin_search.body#}}
            淘宝数据: {{#taobao_search.body#}}
            亚马逊数据: {{#amazon_search.body#}}
            拼多多数据: {{#pdd_search.body#}}
        selected: false
        structured_output:
          schema:
            type: object
            properties:
              category:
                type: string
                description: 商品类目
              platforms:
                type: object
                properties:
                  douyin:
                    type: object
                    properties:
                      market_trend:
                        type: string
                        description: 抖音市场趋势
                      hot_products:
                        type: array
                        items:
                          type: string
                        description: 热门产品列表
                      opportunity_score:
                        type: number
                        minimum: 0
                        maximum: 100
                        description: 机会评分
                    required: ["market_trend", "hot_products", "opportunity_score"]
                  taobao:
                    type: object
                    properties:
                      market_trend:
                        type: string
                        description: 淘宝市场趋势
                      hot_products:
                        type: array
                        items:
                          type: string
                        description: 热门产品列表
                      opportunity_score:
                        type: number
                        minimum: 0
                        maximum: 100
                        description: 机会评分
                    required: ["market_trend", "hot_products", "opportunity_score"]
                  amazon:
                    type: object
                    properties:
                      market_trend:
                        type: string
                        description: 亚马逊市场趋势
                      hot_products:
                        type: array
                        items:
                          type: string
                        description: 热门产品列表
                      opportunity_score:
                        type: number
                        minimum: 0
                        maximum: 100
                        description: 机会评分
                    required: ["market_trend", "hot_products", "opportunity_score"]
                  pinduoduo:
                    type: object
                    properties:
                      market_trend:
                        type: string
                        description: 拼多多市场趋势
                      hot_products:
                        type: array
                        items:
                          type: string
                        description: 热门产品列表
                      opportunity_score:
                        type: number
                        minimum: 0
                        maximum: 100
                        description: 机会评分
                    required: ["market_trend", "hot_products", "opportunity_score"]
                required: ["douyin", "taobao", "amazon", "pinduoduo"]
              overall_analysis:
                type: object
                properties:
                  market_size:
                    type: string
                    enum: ["大", "中", "小"]
                    description: 市场规模
                  competition_level:
                    type: string
                    enum: ["高", "中", "低"]
                    description: 竞争程度
                  profit_potential:
                    type: string
                    enum: ["高", "中", "低"]
                    description: 盈利潜力
                  recommended_products:
                    type: array
                    items:
                      type: string
                    description: 推荐产品
                  market_gaps:
                    type: array
                    items:
                      type: string
                    description: 市场空白
                  risk_factors:
                    type: array
                    items:
                      type: string
                    description: 风险因素
                required: ["market_size", "competition_level", "profit_potential", "recommended_products", "market_gaps", "risk_factors"]
              scores:
                type: object
                properties:
                  overall_opportunity:
                    type: number
                    minimum: 0
                    maximum: 100
                    description: 总体机会评分
                  market_potential:
                    type: number
                    minimum: 0
                    maximum: 100
                    description: 市场潜力评分
                  competition_risk:
                    type: number
                    minimum: 0
                    maximum: 100
                    description: 竞争风险评分
                required: ["overall_opportunity", "market_potential", "competition_risk"]
            required: ["category", "platforms", "overall_analysis", "scores"]
        structured_output_enabled: true
        title: 结构化分析
        type: llm
        variables: []
        vision:
          enabled: false
      height: 96
      id: structured_analysis
      position:
        x: -100
        y: 350
      positionAbsolute:
        x: -100
        y: 350
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 评分计算器节点（变量赋值器）
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: assign
          value:
          - structured_analysis
          - structured_output
          - platforms
          - douyin
          - opportunity_score
          variable_selector:
          - score_calculator
          - douyin_score
          write_mode: over-write
        - input_type: variable
          operation: assign
          value:
          - structured_analysis
          - structured_output
          - platforms
          - taobao
          - opportunity_score
          variable_selector:
          - score_calculator
          - taobao_score
          write_mode: over-write
        - input_type: variable
          operation: assign
          value:
          - structured_analysis
          - structured_output
          - platforms
          - amazon
          - opportunity_score
          variable_selector:
          - score_calculator
          - amazon_score
          write_mode: over-write
        - input_type: variable
          operation: assign
          value:
          - structured_analysis
          - structured_output
          - platforms
          - pinduoduo
          - opportunity_score
          variable_selector:
          - score_calculator
          - pdd_score
          write_mode: over-write
        - input_type: constant
          operation: assign
          value: "({{#score_calculator.douyin_score#}} + {{#score_calculator.taobao_score#}} + {{#score_calculator.amazon_score#}} + {{#score_calculator.pdd_score#}}) / 4"
          variable_selector:
          - score_calculator
          - average_platform_score
          write_mode: over-write
        - input_type: variable
          operation: assign
          value:
          - structured_analysis
          - structured_output
          - scores
          - overall_opportunity
          variable_selector:
          - score_calculator
          - overall_opportunity
          write_mode: over-write
        - input_type: variable
          operation: assign
          value:
          - structured_analysis
          - structured_output
          - scores
          - market_potential
          variable_selector:
          - score_calculator
          - market_potential
          write_mode: over-write
        - input_type: variable
          operation: assign
          value:
          - structured_analysis
          - structured_output
          - scores
          - competition_risk
          variable_selector:
          - score_calculator
          - competition_risk
          write_mode: over-write
        - input_type: constant
          operation: assign
          value: "{{#score_calculator.overall_opportunity#}} >= 80 ? '高' : ({{#score_calculator.overall_opportunity#}} >= 60 ? '中' : '低')"
          variable_selector:
          - score_calculator
          - recommendation_level
          write_mode: over-write
        - input_type: constant
          operation: assign
          value: "{{#score_calculator.competition_risk#}} >= 70 ? '需谨慎' : ({{#score_calculator.competition_risk#}} >= 40 ? '适中' : '风险较低')"
          variable_selector:
          - score_calculator
          - risk_level
          write_mode: over-write
        selected: false
        title: 评分计算器
        type: assigner
        version: '2'
      height: 172
      id: score_calculator
      position:
        x: 200
        y: 350
      positionAbsolute:
        x: 200
        y: 350
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 格式化输出节点
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.1
            max_tokens: 1800
          mode: chat
          name: gpt-3.5-turbo
          provider: openai
        prompt_template:
        - role: system
          text: |
            请基于结构化数据和计算结果，生成详细的选品分析报告。
            
            使用以下模板：
            
            # 📊 {{category}} 电商选品分析报告
            
            ## 🏆 综合评级：{{recommendation_level}}
            
            ## 🏪 各平台机会评分
            - **抖音**: {{douyin_score}}分 
            - **淘宝**: {{taobao_score}}分 
            - **亚马逊**: {{amazon_score}}分
            - **拼多多**: {{pdd_score}}分
            - **平均分**: {{average_platform_score}}分
            
            ## 🎯 推荐产品
            {{recommended_products_list}}
            
            ## 💰 市场机会分析
            - **市场规模**: {{market_size}}
            - **竞争程度**: {{competition_level}}
            - **盈利潜力**: {{profit_potential}}
            - **风险等级**: {{risk_level}}
            
            ## ⚡ 市场空白机会
            {{market_gaps_list}}
            
            ## ⚠️ 风险提示
            {{risk_factors_list}}
            
            ## 📈 详细评分
            - **总体机会**: {{overall_opportunity}}/100 
            - **市场潜力**: {{market_potential}}/100
            - **竞争风险**: {{competition_risk}}/100
            - **平台平均**: {{average_platform_score}}/100
            
            ## 💡 投资建议
            基于 {{recommendation_level}} 评级和 {{risk_level}} 风险等级，建议采取相应策略。
        - role: user
          text: |
            请基于以下数据生成选品报告：
            
            结构化分析: {{#structured_analysis.structured_output#}}
            
            计算结果:
            - 抖音评分: {{#score_calculator.douyin_score#}}
            - 淘宝评分: {{#score_calculator.taobao_score#}}
            - 亚马逊评分: {{#score_calculator.amazon_score#}}
            - 拼多多评分: {{#score_calculator.pdd_score#}}
            - 平均评分: {{#score_calculator.average_platform_score#}}
            - 推荐等级: {{#score_calculator.recommendation_level#}}
            - 风险等级: {{#score_calculator.risk_level#}}
        selected: false
        title: 智能报告生成
        type: llm
        variables: []
        vision:
          enabled: false
      height: 96
      id: format_output
      position:
        x: 500
        y: 350
      positionAbsolute:
        x: 500
        y: 350
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    # 最终答案节点
    - data:
        answer: |
          {{#format_output.text#}}
          
          ---
          
          ### 🔧 技术统计
          - **数据源**: 4个平台并行搜索 ✅
          - **结构化分析**: JSON格式输出 ✅ 
          - **变量计算**: 智能评分系统 ✅
          - **报告生成**: 动态模板渲染 ✅
          
          ### 📊 计算细节
          ```
          平台评分:
          - 抖音: {{#score_calculator.douyin_score#}}/100
          - 淘宝: {{#score_calculator.taobao_score#}}/100
          - 亚马逊: {{#score_calculator.amazon_score#}}/100
          - 拼多多: {{#score_calculator.pdd_score#}}/100
          
          综合指标:
          - 平均分: {{#score_calculator.average_platform_score#}}/100
          - 推荐等级: {{#score_calculator.recommendation_level#}}
          - 风险等级: {{#score_calculator.risk_level#}}
          ```
          
          💡 **下一步**: 我们将添加最终优化和完整功能集成！
        desc: ''
        selected: false
        title: 智能选品分析报告
        type: answer
        variables: []
      height: 185
      id: final_answer
      position:
        x: 800
        y: 350
      positionAbsolute:
        x: 800
        y: 350
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244

    viewport:
      x: 700
      y: -200
      zoom: 0.6 