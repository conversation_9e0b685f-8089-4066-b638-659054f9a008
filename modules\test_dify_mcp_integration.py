#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Dify工作流中的MCP集成
模拟Dify工作流调用MCP服务器的过程
"""

import asyncio
import json
import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

async def test_dify_mcp_integration():
    """测试Dify工作流中的MCP集成"""
    print("🔗 测试Dify工作流MCP集成")
    print("=" * 50)
    
    # 模拟从Dify工作流传入的参数
    test_scenarios = [
        {
            "keyword": "蓝牙耳机",
            "category": "数码配件",
            "confidence": 0.95,
            "description": "用户输入: 我想分析蓝牙耳机的市场机会"
        },
        {
            "keyword": "运动健身",
            "category": "运动户外",
            "confidence": 0.88,
            "description": "用户输入: 帮我看看运动健身类产品怎么样"
        },
        {
            "keyword": "手机壳",
            "category": "手机配件",
            "confidence": 0.92,
            "description": "用户输入: 手机壳好卖吗？"
        }
    ]
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n🧪 测试场景 {i}: {scenario['description']}")
        print("-" * 40)
        
        result = await call_mcp_crawler_for_dify(
            scenario['keyword'],
            scenario['category'], 
            scenario['confidence']
        )
        
        print(f"✅ 测试结果:")
        print(f"   成功状态: {result.get('success', False)}")
        print(f"   平台: {result.get('platform', 'N/A')}")
        print(f"   关键词: {result.get('keyword', 'N/A')}")
        print(f"   数据源: {result.get('source', 'N/A')}")
        
        if result.get('success') and result.get('mcp_data'):
            mcp_data = result['mcp_data']
            if mcp_data.get('success'):
                summary = mcp_data.get('data', {}).get('summary', {})
                print(f"   商品数量: {summary.get('total_products', 0)}")
                print(f"   评价数量: {summary.get('total_reviews', 0)}")
                print(f"   爬虫版本: {mcp_data.get('crawler_info', {}).get('version', 'N/A')}")
            else:
                print(f"   爬虫错误: {mcp_data.get('error', '未知错误')}")
        elif not result.get('success'):
            print(f"   集成错误: {result.get('error', '未知错误')}")

async def call_mcp_crawler_for_dify(keyword, category, confidence):
    """
    为Dify工作流调用MCP淘宝爬虫
    这个函数模拟了Dify工作流中Code Execution节点的逻辑
    """
    try:
        # MCP服务器配置
        server_path = r"c:\Users\<USER>\Desktop\dify_test\modules\taobao_mcp_server.py"
        python_path = "python"
        
        print(f"   🔧 连接MCP服务器: {server_path}")
        print(f"   📊 搜索参数: {keyword} (分类: {category}, 置信度: {confidence})")
        
        # 创建MCP客户端参数
        server_params = StdioServerParameters(
            command=python_path,
            args=[server_path],
            env=os.environ.copy()
        )
        
        # 连接MCP服务器并调用工具
        async with stdio_client(server_params) as (read_stream, write_stream):
            async with ClientSession(read_stream, write_stream) as session:
                # 初始化连接
                await session.initialize()
                print("   ✅ MCP连接初始化成功")
                
                # 调用爬虫工具
                result = await session.call_tool(
                    "crawl_taobao_products",
                    arguments={
                        'keyword': keyword,
                        'max_products': 2,  # 测试用较小数量
                        'max_reviews': 5,   # 测试用较小数量
                        'include_reviews': True,
                        'enable_debug': False
                    }
                )
                print("   ✅ MCP工具调用成功")
                
                # 解析结果
                if result.content:
                    content = result.content[0]
                    if hasattr(content, 'text'):
                        try:
                            data = json.loads(content.text)
                            return {
                                "success": True,
                                "platform": "taobao",
                                "keyword": keyword,
                                "category": category,
                                "confidence": confidence,
                                "mcp_data": data,
                                "source": "MCP Protocol v1.12.2"
                            }
                        except json.JSONDecodeError:
                            return {
                                "success": False,
                                "error": "MCP返回数据格式错误",
                                "raw_content": content.text[:500]
                            }
                
                return {"success": False, "error": "MCP无返回内容"}
                
    except Exception as e:
        return {
            "success": False,
            "error": f"MCP调用失败: {str(e)}",
            "platform": "taobao"
        }

async def test_mcp_server_availability():
    """测试MCP服务器可用性"""
    print("\n🔍 测试MCP服务器可用性")
    print("-" * 30)
    
    try:
        server_path = r"c:\Users\<USER>\Desktop\dify_test\modules\taobao_mcp_server.py"
        server_params = StdioServerParameters(
            command="python",
            args=[server_path],
            env=os.environ.copy()
        )
        
        async with stdio_client(server_params) as (read_stream, write_stream):
            async with ClientSession(read_stream, write_stream) as session:
                await session.initialize()
                
                # 测试工具列表
                tools = await session.list_tools()
                print(f"✅ MCP服务器可用")
                print(f"   发现工具数量: {len(tools.tools)}")
                for tool in tools.tools:
                    print(f"   - {tool.name}: {tool.description}")
                
                # 测试状态工具
                status_result = await session.call_tool("get_crawler_status", arguments={})
                if status_result.content:
                    content = status_result.content[0]
                    if hasattr(content, 'text'):
                        try:
                            status_data = json.loads(content.text)
                            print(f"   服务状态: {status_data.get('status', 'unknown')}")
                            print(f"   服务版本: {status_data.get('version', 'unknown')}")
                        except json.JSONDecodeError:
                            print("   状态数据解析失败")
                
                return True
                
    except Exception as e:
        print(f"❌ MCP服务器不可用: {e}")
        return False

if __name__ == "__main__":
    async def main():
        # 首先测试服务器可用性
        server_available = await test_mcp_server_availability()
        
        if server_available:
            # 然后测试集成功能
            await test_dify_mcp_integration()
            
            print("\n🎉 Dify MCP集成测试完成!")
            print("💡 现在可以在Dify工作流中使用MCP淘宝爬虫了")
        else:
            print("\n❌ 请先确保MCP服务器正常运行")
    
    asyncio.run(main())
