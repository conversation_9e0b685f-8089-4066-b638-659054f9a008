#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试淘宝爬虫MCP服务器
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from taobao_mcp_server import TaobaoMCPServer

async def test_mcp_server():
    """测试MCP服务器功能"""
    print("🚀 开始测试淘宝爬虫MCP服务器")
    print("=" * 50)
    
    try:
        # 创建服务器实例
        server = TaobaoMCPServer()
        print(f"✅ 服务器实例创建成功: {server.name} v{server.version}")
        
        # 测试状态获取
        print("\n📊 测试状态获取...")
        status_result = await server.get_status()
        print("状态信息:")
        print(status_result[0].text)
        
        # 测试简单爬取
        print("\n🔍 测试商品爬取...")
        test_args = {
            'keyword': '蓝牙耳机',
            'max_products': 2,
            'max_reviews': 5,
            'include_reviews': True,
            'enable_debug': True
        }
        
        print(f"测试参数: {test_args}")
        crawl_result = await server.crawl_products(test_args)
        print("爬取结果:")
        print(crawl_result[0].text)
        
        print("\n✅ MCP服务器测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_mcp_server())
