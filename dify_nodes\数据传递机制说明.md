# 🔗 Dify工作流数据传递机制详解

## 🎯 问题：淘宝爬虫如何获取前一个节点的输出？

### 📊 数据流向图

```
🧠 智能关键词提取器
├── 输出: structured_output
│   ├── keyword: "蓝牙耳机"
│   ├── category: "消费电子"
│   └── confidence: 1.0
│
↓ (数据传递)
│
🎯 最终版淘宝爬虫 (临时模拟)
├── 接收方式: URL Query Parameters
├── 处理: httpbin.org/get?keyword=蓝牙耳机&category=消费电子...
└── 输出: 模拟的淘宝数据
```

## 🔧 当前数据传递配置

### 1. 关键词提取器输出格式
```json
{
  "structured_output": {
    "keyword": "蓝牙耳机",
    "category": "消费电子", 
    "confidence": 1.0
  }
}
```

### 2. 淘宝爬虫接收配置
```yaml
# 改进后的配置
url: "https://httpbin.org/get?platform=taobao&keyword={{#keyword_extractor.structured_output.keyword#}}&category={{#keyword_extractor.structured_output.category#}}&confidence={{#keyword_extractor.structured_output.confidence#}}"
method: "GET"
headers: "Content-Type: application/json\nUser-Agent: Dify-TaobaoBot/1.0"
```

### 3. 实际请求示例
当用户输入"我想分析蓝牙耳机的市场机会"时：

**生成的URL**：
```
https://httpbin.org/get?platform=taobao&keyword=蓝牙耳机&category=消费电子&confidence=1.0
```

**httpbin.org的响应**：
```json
{
  "args": {
    "platform": "taobao",
    "keyword": "蓝牙耳机",
    "category": "消费电子",
    "confidence": "1.0"
  },
  "headers": {
    "Content-Type": "application/json",
    "User-Agent": "Dify-TaobaoBot/1.0"
  },
  "url": "https://httpbin.org/get?platform=taobao&keyword=蓝牙耳机&category=消费电子&confidence=1.0"
}
```

## 📋 Dify变量引用语法

### 基本语法
```yaml
{{#节点ID.输出字段.子字段#}}
```

### 具体示例
| 引用 | 说明 | 示例值 |
|------|------|--------|
| `{{#keyword_extractor.structured_output.keyword#}}` | 提取的关键词 | "蓝牙耳机" |
| `{{#keyword_extractor.structured_output.category#}}` | 商品分类 | "消费电子" |
| `{{#keyword_extractor.structured_output.confidence#}}` | 置信度 | 1.0 |
| `{{#keyword_extractor.text#}}` | 原始文本输出 | 完整JSON字符串 |

## 🔄 不同数据传递方式对比

### 方式1: HTTP Headers (之前的方式)
```yaml
headers: "Keyword: {{#keyword_extractor.structured_output.keyword#}}"
```
- ✅ 简单直接
- ❌ httpbin.org不处理自定义headers
- ❌ 数据不可见

### 方式2: Query Parameters (当前方式)
```yaml
url: "https://httpbin.org/get?keyword={{#keyword_extractor.structured_output.keyword#}}"
```
- ✅ 数据在响应中可见
- ✅ 便于调试和验证
- ✅ httpbin.org会返回所有参数

### 方式3: POST Body (真实API方式)
```yaml
method: "POST"
body: |
  {
    "keyword": "{{#keyword_extractor.structured_output.keyword#}}",
    "max_products": 10
  }
```
- ✅ 支持复杂数据结构
- ✅ 适合真实API调用
- ❌ httpbin.org的POST响应不够直观

## 🧪 验证数据传递

### 测试步骤
1. **输入**: "我想分析蓝牙耳机的市场机会"
2. **关键词提取**: 应该输出"蓝牙耳机"
3. **淘宝节点**: 检查生成的URL是否包含正确参数
4. **响应验证**: httpbin.org应该返回包含参数的JSON

### 预期响应
```json
{
  "args": {
    "platform": "taobao",
    "keyword": "蓝牙耳机",
    "category": "消费电子",
    "confidence": "1.0"
  },
  "headers": {
    "Content-Type": "application/json",
    "User-Agent": "Dify-TaobaoBot/1.0"
  },
  "origin": "xxx.xxx.xxx.xxx",
  "url": "https://httpbin.org/get?platform=taobao&keyword=蓝牙耳机&category=消费电子&confidence=1.0"
}
```

## 🔧 真实API恢复配置

当API修复后，恢复为真实配置：

```yaml
# 真实API配置
url: "{{#env.TAOBAO_CRAWLER_API_URL#}}"
method: "POST"
headers: "Content-Type: application/json"
body: |
  {
    "keyword": "{{#keyword_extractor.structured_output.keyword#}}",
    "max_products": 10,
    "max_reviews": 60,
    "include_reviews": true,
    "enable_debug": false
  }
```

## 🎯 数据传递最佳实践

### 1. 结构化输出优于文本输出
```yaml
# ✅ 推荐
{{#keyword_extractor.structured_output.keyword#}}

# ❌ 不推荐  
{{#keyword_extractor.text#}}
```

### 2. 明确的字段引用
```yaml
# ✅ 明确
{{#keyword_extractor.structured_output.keyword#}}

# ❌ 模糊
{{#keyword_extractor.keyword#}}
```

### 3. 适当的数据验证
```yaml
# 在LLM节点中设置required字段
required: ["keyword", "category", "confidence"]
```

## 🔍 调试技巧

### 1. 查看节点输出
在Dify执行日志中查看每个节点的实际输出

### 2. 使用httpbin.org验证
临时使用httpbin.org可以清楚看到传递的数据

### 3. 检查变量引用
确保节点ID和字段名完全匹配

## 📊 总结

**当前数据传递机制**：
1. 🧠 关键词提取器输出结构化数据
2. 🔗 通过Dify变量引用语法传递数据
3. 🎯 淘宝节点通过URL参数接收数据
4. 📡 httpbin.org返回包含所有参数的响应
5. 🧠 后续节点可以使用这个响应进行分析

这样确保了数据在工作流中的正确传递和处理！
